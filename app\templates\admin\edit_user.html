{% extends "base.html" %}

{% block title %}Edit User: {{ user.username }} - Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-pencil"></i> Edit User: {{ user.username }}</h1>
        <a href="{{ url_for('views.admin_users') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> Back to Users
        </a>
    </div>

    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-person-gear"></i> Edit User Account</h5>
                </div>
                <div class="card-body">
                    <form method="POST" id="editUserForm">
                        <div class="mb-3">
                            <label for="username" class="form-label">Username *</label>
                            <input type="text" class="form-control" id="username" name="username" 
                                   value="{{ user.username }}" minlength="3" maxlength="50" required>
                            <div class="form-text">At least 3 characters, letters and numbers only</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address *</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ user.email }}" required>
                            <div class="form-text">User's email address for notifications</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="role" class="form-label">Account Role *</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="publisher" {% if user.role == 'publisher' %}selected{% endif %}>
                                    Publisher - Can upload and manage their own apps
                                </option>
                                <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>
                                    Administrator - Full system access
                                </option>
                            </select>
                            <div class="form-text">
                                <strong>Publisher:</strong> Limited to managing their own apps<br>
                                <strong>Admin:</strong> Can manage all apps and users
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="password" name="password" 
                                   minlength="6">
                            <div class="form-text">Leave blank to keep current password. Minimum 6 characters if changing.</div>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Warning:</strong> Changing the user's role or password will affect their access to the system. 
                            Make sure to inform the user of any changes.
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="button" class="btn btn-secondary me-2" onclick="history.back()">Cancel</button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> Update User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- User Info Sidebar -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-info-circle"></i> User Information</h5>
                </div>
                <div class="card-body">
                    <table class="table table-sm">
                        <tr>
                            <td><strong>User ID:</strong></td>
                            <td>{{ user.id }}</td>
                        </tr>
                        <tr>
                            <td><strong>Current Role:</strong></td>
                            <td>
                                {% if user.role == 'admin' %}
                                <span class="badge bg-danger">
                                    <i class="bi bi-shield-fill"></i> Admin
                                </span>
                                {% else %}
                                <span class="badge bg-primary">
                                    <i class="bi bi-person"></i> Publisher
                                </span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Apps Published:</strong></td>
                            <td>{{ user.apps|length }}</td>
                        </tr>
                        <tr>
                            <td><strong>Total Downloads:</strong></td>
                            <td>{{ user.apps|sum(attribute='downloads') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Account Created:</strong></td>
                            <td>{{ user.created_at.strftime('%m/%d/%Y') }}</td>
                        </tr>
                        <tr>
                            <td><strong>Last Login:</strong></td>
                            <td>
                                {% if user.last_login %}
                                {{ user.last_login.strftime('%m/%d/%Y') }}
                                {% else %}
                                <span class="text-muted">Never</span>
                                {% endif %}
                            </td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                {% if user.is_active %}
                                <span class="badge bg-success">Active</span>
                                {% else %}
                                <span class="badge bg-danger">Inactive</span>
                                {% endif %}
                            </td>
                        </tr>
                    </table>
                </div>
            </div>

            {% if user.apps %}
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-list"></i> User's Apps</h5>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        {% for app in user.apps[:5] %}
                        <div class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <strong>{{ app.name }}</strong><br>
                                <small class="text-muted">{{ app.downloads }} downloads</small>
                            </div>
                            <a href="{{ url_for('app_detail', app_id=app.id) }}" class="btn btn-sm btn-outline-primary">
                                View
                            </a>
                        </div>
                        {% endfor %}
                        {% if user.apps|length > 5 %}
                        <div class="list-group-item text-center">
                            <small class="text-muted">... and {{ user.apps|length - 5 }} more apps</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Username validation
document.getElementById('username').addEventListener('input', function() {
    const username = this.value;
    const regex = /^[a-zA-Z0-9_]+$/;
    
    if (username && !regex.test(username)) {
        this.setCustomValidity('Username can only contain letters, numbers, and underscores');
    } else {
        this.setCustomValidity('');
    }
});

// Role selection helper
document.getElementById('role').addEventListener('change', function() {
    const role = this.value;
    const helpText = this.nextElementSibling;
    
    if (role === 'admin') {
        helpText.innerHTML = '<strong class="text-warning">⚠️ Admin:</strong> Full system access - can manage all apps and users';
    } else if (role === 'publisher') {
        helpText.innerHTML = '<strong class="text-info">📝 Publisher:</strong> Limited to managing their own apps';
    }
});

// Password validation
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const helpText = this.nextElementSibling;
    
    if (password && password.length < 6) {
        helpText.innerHTML = '<span class="text-danger">Password must be at least 6 characters long</span>';
        this.setCustomValidity('Password must be at least 6 characters long');
    } else {
        helpText.innerHTML = 'Leave blank to keep current password. Minimum 6 characters if changing.';
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
