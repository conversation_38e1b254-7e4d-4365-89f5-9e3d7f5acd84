{% extends "base.html" %}

{% block title %}Manage Users - Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-people"></i> Manage Users</h1>
        <div>
            <a href="{{ url_for('views.admin_dashboard') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('views.admin_add_user') }}" class="btn btn-success">
                <i class="bi bi-person-plus"></i> Add New User
            </a>
        </div>
    </div>

    {% if users %}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Apps Published</th>
                            <th>Created</th>
                            <th>Last Login</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                <strong>{{ user.username }}</strong>
                                {% if user.id == session.user_id %}
                                <span class="badge bg-info ms-2">You</span>
                                {% endif %}
                            </td>
                            <td>{{ user.email }}</td>
                            <td>
                                {% if user.role == 'admin' %}
                                <span class="badge bg-danger">
                                    <i class="bi bi-shield-fill"></i> Admin
                                </span>
                                {% else %}
                                <span class="badge bg-primary">
                                    <i class="bi bi-person"></i> Publisher
                                </span>
                                {% endif %}
                            </td>
                            <td>
                                {% set user_apps = user.apps %}
                                {{ user_apps|length }} apps
                                {% if user_apps|length > 0 %}
                                <br><small class="text-muted">
                                    {{ user_apps|sum(attribute='downloads') }} total downloads
                                </small>
                                {% endif %}
                            </td>
                            <td>
                                {{ user.created_at | datetime('%m/%d/%Y') }}
                            </td>
                            <td>
                                {% if user.last_login %}
                                    {{ user.last_login | datetime('%m/%d/%Y') }}
                                {% else %}
                                    <span class="text-muted">Never</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    {% if user.id != session.user_id %}
                                    <button class="btn btn-sm btn-outline-warning" 
                                            data-user-id="{{ user.id }}"
                                            data-username="{{ user.username|e }}"
                                            data-email="{{ user.email|e }}"
                                            data-role="{{ user.role|e }}"
                                            onclick="editUserFromButton(this)"
                                            title="Edit User">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" 
                                            data-user-id="{{ user.id }}"
                                            data-username="{{ user.username|e }}"
                                            onclick="confirmDeleteFromButton(this)"
                                            title="Delete User">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                    {% else %}
                                    <span class="text-muted small">Current User</span>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% else %}
    <div class="card">
        <div class="card-body text-center py-5">
            <i class="bi bi-people display-1 text-muted"></i>
            <h3 class="mt-3">No Users Found</h3>
            <p class="text-muted">Start by creating the first user account.</p>
            <a href="{{ url_for('views.admin_add_user') }}" class="btn btn-success">
                <i class="bi bi-person-plus"></i> Add First User
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUserName"></strong>?</p>
                <p class="text-danger"><i class="bi bi-exclamation-triangle"></i> This action cannot be undone. All apps published by this user will also be deleted.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_scripts %}
<script>
function confirmDelete(userId, username) {
    document.getElementById('deleteUserName').textContent = username;
    document.getElementById('deleteForm').action = `/admin/delete_user/${userId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

function editUser(userId, username, email, role) {
    // Redirect to edit user page instead of using modal
    window.location.href = `/admin/edit_user/${userId}`;
}

function confirmDeleteFromButton(btn) {
    const userId = btn.getAttribute('data-user-id');
    const username = btn.getAttribute('data-username');
    confirmDelete(userId, username);
}

function editUserFromButton(btn) {
    const userId = btn.getAttribute('data-user-id');
    const username = btn.getAttribute('data-username');
    const email = btn.getAttribute('data-email');
    const role = btn.getAttribute('data-role');
    editUser(userId, username, email, role);
}
</script>
{% endblock %}
