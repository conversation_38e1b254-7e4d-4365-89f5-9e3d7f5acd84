{% extends "base.html" %}

{% block title %}Publisher Dashboard - PEPE Store{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-house"></i> Publisher Dashboard</h1>
        <div>
            <a href="{{ url_for('views.publisher_add_app') }}" class="btn btn-success">
                <i class="bi bi-plus-circle"></i> Add New App
            </a>
        </div>
    </div>

    <!-- Welcome Message -->
    <div class="alert alert-info">
        <h5><i class="bi bi-person-circle"></i> Welcome, {{ user.username }}!</h5>
        <p class="mb-0">You have published {{ user_apps|length }} apps with a total of {{ total_user_downloads }} downloads.</p>
    </div>

    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ user_apps|length }}</h4>
                            <p class="mb-0">My Apps</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-app-indicator display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ total_user_downloads }}</h4>
                            <p class="mb-0">Total Downloads</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-download display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ user_apps|selectattr('is_featured')|list|length }}</h4>
                            <p class="mb-0">Featured Apps</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-star-fill display-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- My Apps -->
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0">My Published Apps</h5>
            <a href="{{ url_for('views.publisher_add_app') }}" class="btn btn-sm btn-success">
                <i class="bi bi-plus-circle"></i> Add App
            </a>
        </div>
        <div class="card-body">
            {% if user_apps %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Downloads</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for app in user_apps %}
                        <tr>
                            <td>
                                <strong>{{ app.name }}</strong><br>
                                <small class="text-muted">{{ app.short_description[:50] }}{% if app.short_description|length > 50 %}...{% endif %}</small>
                            </td>
                            <td><span class="badge bg-secondary">{{ app.category }}</span></td>
                            <td>
                                {% if app.price == 0 %}
                                <span class="text-success">FREE</span>
                                {% else %}
                                <span class="text-primary">${{ "%.2f"|format(app.price) }}</span>
                                {% endif %}
                            </td>
                            <td>{{ app.downloads }}</td>
                            <td>
                                {% if app.is_featured %}
                                <span class="badge bg-warning text-dark">
                                    <i class="bi bi-star-fill"></i> Featured
                                </span>
                                {% else %}
                                <span class="badge bg-light text-dark">Published</span>
                                {% endif %}
                            </td>
                            <td>{{ app.created_at | datetime('%m/%d/%Y') }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('views.app_detail', app_id=app.id) }}" 
                                       class="btn btn-sm btn-outline-info" title="View">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url_for('views.publisher_edit_app', app_id=app.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="bi bi-app display-1 text-muted"></i>
                <h3 class="mt-3">No apps published yet</h3>
                <p class="text-muted">Start by publishing your first app to the PEPE Store.</p>
                <a href="{{ url_for('views.publisher_add_app') }}" class="btn btn-success">
                    <i class="bi bi-plus-circle"></i> Publish First App
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
