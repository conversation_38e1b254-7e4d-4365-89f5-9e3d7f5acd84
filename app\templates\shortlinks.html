{% extends "base.html" %}

{% block title %}Shortlink Manager - App Store{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('views.index') }}">Home</a></li>
            {% if user_role == 'admin' %}
            <li class="breadcrumb-item"><a href="{{ url_for('views.admin_dashboard') }}">Admin</a></li>
            {% else %}
            <li class="breadcrumb-item"><a href="{{ url_for('views.publisher_dashboard') }}">Publisher</a></li>
            {% endif %}
            <li class="breadcrumb-item active">Shortlinks</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8">
            <!-- Shortlinks List -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3><i class="bi bi-link-45deg"></i> Your Shortlinks</h3>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createShortlinkModal">
                        <i class="bi bi-plus"></i> Create Shortlink
                    </button>
                </div>
                <div class="card-body">
                    {% if shortlinks %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Short Code</th>
                                    <th>Title</th>
                                    <th>Original URL</th>
                                    <th>Clicks</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for link in shortlinks %}
                                <tr>
                                    <td>
                                        <code>{{ link.short_code }}</code>
                                        <br>
                                        <small class="text-muted">{{ request.host_url }}s/{{ link.short_code }}</small>
                                    </td>
                                    <td>
                                        {{ link.title or 'Untitled' }}
                                        {% if link.app_name %}
                                        <br><small class="text-muted">App: {{ link.app_name }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ link.original_url }}" target="_blank" class="text-truncate d-block" style="max-width: 200px;">
                                            {{ link.original_url }}
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ link.click_count }}</span>
                                    </td>
                                    <td>
                                        <small>{{ link.created_at | datetime('%m/%d/%Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button type="button" class="btn btn-outline-primary" onclick="copyShortlink('{{ request.host_url }}s/{{ link.short_code }}')">
                                                <i class="bi bi-clipboard"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-info" onclick="viewAnalytics({{ link.id }})">
                                                <i class="bi bi-graph-up"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-secondary" onclick="editShortlink({{ link.id }})">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-outline-danger" onclick="deleteShortlink({{ link.id }})">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-link-45deg display-1 text-muted"></i>
                        <h4 class="text-muted">No Shortlinks Yet</h4>
                        <p class="text-muted">Create your first shortlink to get started!</p>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createShortlinkModal">
                            <i class="bi bi-plus"></i> Create Shortlink
                        </button>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Quick Stats -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-graph-up"></i> Quick Stats</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <h3 class="text-primary">{{ shortlinks|length }}</h3>
                            <small class="text-muted">Total Links</small>
                        </div>
                        <div class="col-6">
                            <h3 class="text-success">{{ shortlinks|sum(attribute='click_count') }}</h3>
                            <small class="text-muted">Total Clicks</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Features -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-star"></i> Features</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success"></i> Custom short codes</li>
                        <li><i class="bi bi-check-circle text-success"></i> Click analytics</li>
                        <li><i class="bi bi-check-circle text-success"></i> App integration</li>
                        <li><i class="bi bi-check-circle text-success"></i> Expiration dates</li>
                        <li><i class="bi bi-check-circle text-success"></i> Bulk management</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Shortlink Modal -->
<div class="modal fade" id="createShortlinkModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Shortlink</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createShortlinkForm">
                    <div class="mb-3">
                        <label for="originalUrl" class="form-label">Original URL:</label>
                        <input type="url" class="form-control" id="originalUrl" required
                               placeholder="https://example.com/your-link">
                    </div>
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">Title (Optional):</label>
                        <input type="text" class="form-control" id="title"
                               placeholder="Descriptive title for your link">
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description (Optional):</label>
                        <textarea class="form-control" id="description" rows="2"
                                  placeholder="Brief description of the link"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="customCode" class="form-label">Custom Code (Optional):</label>
                        <input type="text" class="form-control" id="customCode"
                               placeholder="custom-code" pattern="[a-zA-Z0-9-_]+">
                        <small class="text-muted">Leave empty for auto-generated code. Use only letters, numbers, hyphens, and underscores.</small>
                    </div>
                    
                    {% if user_apps %}
                    <div class="mb-3">
                        <label for="appId" class="form-label">Link to App (Optional):</label>
                        <select class="form-select" id="appId">
                            <option value="">Not linked to any app</option>
                            {% for app in user_apps %}
                            <option value="{{ app.id }}">{{ app.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="createShortlink()">Create Shortlink</button>
            </div>
        </div>
    </div>
</div>

<script>
function createShortlink() {
    const originalUrl = document.getElementById('originalUrl').value;
    const title = document.getElementById('title').value;
    const description = document.getElementById('description').value;
    const customCode = document.getElementById('customCode').value;
    const appId = document.getElementById('appId') ? document.getElementById('appId').value : null;
    
    if (!originalUrl) {
        alert('Please enter a URL.');
        return;
    }
    
    const data = {
        original_url: originalUrl,
        title: title,
        description: description,
        custom_code: customCode,
        app_id: appId ? parseInt(appId) : null
    };
    
    fetch('/shortlinks', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`Shortlink created successfully!\nShort URL: ${data.short_url}`);
            document.getElementById('createShortlinkForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('createShortlinkModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while creating the shortlink.');
    });
}

function copyShortlink(url) {
    navigator.clipboard.writeText(url).then(() => {
        alert('Shortlink copied to clipboard!');
    }).catch(() => {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Shortlink copied to clipboard!');
    });
}

function viewAnalytics(shortlinkId) {
    // TODO: Implement analytics view
    alert('Analytics feature coming soon!');
}

function editShortlink(shortlinkId) {
    // TODO: Implement edit functionality
    alert('Edit feature coming soon!');
}

function deleteShortlink(shortlinkId) {
    if (confirm('Are you sure you want to delete this shortlink?')) {
        // TODO: Implement delete functionality
        alert('Delete feature coming soon!');
    }
}
</script>
{% endblock %}
