from flask import Blueprint, render_template, request, redirect, url_for, flash, session, send_file, jsonify, abort, make_response
try:
    from flask import Markup
except ImportError:
    from markupsafe import Markup
from app.models import User, App, Screenshot, DownloadLog, AdminLog, LoginLog, AppRating, Fingerprint, AbuseReport, Suggestion, Shortlink, Post, get_db
import bleach
import os
import uuid
import hmac
import hashlib
from datetime import datetime, timedelta
import markdown
from werkzeug.utils import secure_filename
from app.config import Config

views = Blueprint('views', __name__)

@views.app_template_filter('markdown')
def markdown_filter(text):
    """Convert markdown to HTML"""
    html = markdown.markdown(text or '', extensions=['fenced_code', 'codehilite'])
    return Markup(html)

@views.app_template_filter('file_size')
def file_size_filter(file_size):
    """Format file size"""
    return App.get_file_size_formatted(file_size)

@views.app_template_filter('has_file')
def has_file_filter(app):
    """Check if app has file"""
    return App.has_file(app)

@views.app_template_filter('download_url')
def download_url_filter(app_id):
    """Get download URL"""
    return App.get_download_url(app_id)

@views.app_template_filter('is_external')
def is_external_filter(app):
    """Check if download is external"""
    return App.is_external_download(app)

@views.app_template_filter('datetime')
def datetime_filter(timestamp, format='%B %d, %Y'):
    """Format datetime or string timestamp"""
    if not timestamp:
        return 'N/A'

    if isinstance(timestamp, str):
        try:
            # Try to parse string timestamp
            for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']:
                try:
                    dt = datetime.strptime(timestamp, fmt)
                    return dt.strftime(format)
                except ValueError:
                    continue
            # If parsing fails, return the string as-is
            return timestamp
        except Exception:
            return timestamp
    elif hasattr(timestamp, 'strftime'):
        # It's already a datetime object
        return timestamp.strftime(format)
    else:
        return str(timestamp)

@views.before_app_request
def check_cookie_auth():
    """Simplified authentication check using fingerprinting"""
    # Allow certain paths without authentication
    allowed_paths = ['/registerf', '/gate', '/static', '/login', '/logout', '/api']
    if any(request.path.startswith(path) for path in allowed_paths):
        return

    # Check if user is logged in via session (admin/publisher access)
    if session.get('user_id'):
        return  # User is logged in, allow access

    # For public access, check fingerprint cookies
    uuid_token = request.cookies.get('uuid')
    fingerprint = request.cookies.get('fp')
    signature = request.cookies.get('sig')

    if not all([uuid_token, fingerprint, signature]):
        return redirect('/gate')

    # Verify the signature
    try:
        fp_data = Fingerprint.get_by_uuid(uuid_token)
        if not fp_data or fp_data['fingerprint'] != fingerprint:
            return redirect('/gate')

        hmac_key = fp_data['hmac_key']
        expected_sig = hmac.new(hmac_key.encode(), f"{uuid_token}:{fingerprint}".encode(), hashlib.sha256).hexdigest()
        if not hmac.compare_digest(expected_sig, signature):
            return redirect('/gate')
    except Exception:
        # If fingerprint verification fails, redirect to gate
        return redirect('/gate')

def sanitize_input(text):
    """Sanitize user input to prevent XSS"""
    if not text:
        return ""
    return bleach.clean(str(text).strip(), tags=[], strip=True)

def validate_numeric_input(value, default=0, min_val=None, max_val=None):
    """Validate and convert numeric input"""
    try:
        num_val = float(value) if value else default
        if min_val is not None and num_val < min_val:
            return default
        if max_val is not None and num_val > max_val:
            return default
        return num_val
    except (ValueError, TypeError):
        return default

def require_admin():
    """Decorator to require admin access"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied - Admin privileges required', 'error')
        return redirect(url_for('views.login'))
    return None

def require_login():
    """Decorator to require user login"""
    if not session.get('user_id'):
        flash('Please log in to access this page', 'error')
        return redirect(url_for('views.login'))
    return None

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

def handle_file_upload(file, upload_type='apps'):
    """Handle file upload and return file path"""
    if not file or file.filename == '':
        return None, "No file selected"

    if not allowed_file(file.filename):
        return None, f"File type not allowed. Allowed types: {', '.join(Config.ALLOWED_EXTENSIONS)}"

    # Create secure filename
    filename = secure_filename(file.filename)
    if not filename:
        return None, "Invalid filename"

    # Add timestamp to avoid conflicts
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S_')
    filename = timestamp + filename

    # Create upload path
    upload_path = os.path.join(Config.UPLOAD_FOLDER, upload_type)
    os.makedirs(upload_path, exist_ok=True)

    file_path = os.path.join(upload_path, filename)

    try:
        file.save(file_path)
        # Return relative path for database storage
        return os.path.join(upload_type, filename), None
    except Exception as e:
        return None, f"Failed to save file: {str(e)}"

def get_client_ip():
    """Get client IP address"""
    return request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)

def log_admin_action(user_id, username, action, details=""):
    """Log admin action"""
    AdminLog.create(
        user_id=user_id,
        username=username,
        action=action,
        details=details,
        ip_address=get_client_ip(),
        user_agent=request.headers.get('User-Agent', '')
    )

@views.route('/')
def index():
    """Main page showing apps"""
    try:
        page = max(1, request.args.get('page', 1, type=int))
        category = request.args.get('category', '').strip()
        search = request.args.get('search', '').strip()

        # Get apps with pagination
        per_page = 12
        offset = (page - 1) * per_page

        # Get total count for pagination
        total_apps = App.get_count(category=category, search=search)

        # Calculate pagination variables
        has_prev = page > 1
        has_next = offset + per_page < total_apps

        # Get paginated apps
        apps_page = App.get_all(limit=per_page, offset=offset, category=category, search=search)

        class AppList:
            def __init__(self, items, total, page, per_page, has_prev, has_next):
                self.items = items
                self.total = total
                self.pages = max(1, (total + per_page - 1) // per_page)
                self.page = page
                self.per_page = per_page
                self.has_prev = has_prev
                self.has_next = has_next
                self.prev_num = max(1, page - 1)
                self.next_num = page + 1 if has_next else page

            def iter_pages(self):
                # Show pagination numbers around current page
                start = max(1, self.page - 2)
                end = min(self.pages + 1, self.page + 3)
                return list(range(start, end))

        apps = AppList(apps_page, total_apps, page, per_page, has_prev, has_next)

        # Get featured apps (limit to avoid performance issues)
        featured_apps = App.get_featured(limit=6)
        categories = App.get_categories()

        return render_template('index.html',
                             apps=apps,
                             featured_apps=featured_apps,
                             categories=categories,
                             current_category=category,
                             search_term=search,
                             page=page,
                             has_prev=has_prev,
                             has_next=has_next)

    except Exception as e:
        flash(f'Error loading page: {str(e)}', 'error')
        # Return empty results on error
        return render_template('index.html',
                             apps=type('AppList', (), {'items': [], 'total': 0, 'pages': 1, 'page': 1, 'has_prev': False, 'has_next': False, 'iter_pages': lambda: [1]})(),
                             featured_apps=[],
                             categories=[],
                             current_category='',
                             search_term='',
                             page=1,
                             has_prev=False,
                             has_next=False)

@views.route('/gate')
def gate():
    """Gate page for fingerprinting"""
    return render_template('gate.html')

@views.route('/registerf', methods=['POST'])
def registerf():
    """Register fingerprint"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        fingerprint = data.get('fingerprint', '').strip()
        browser_data = str(data.get('browser_data', ''))

        if not fingerprint:
            return jsonify({'success': False, 'error': 'Fingerprint is required'}), 400

        # Check if fingerprint exists
        fp_data = Fingerprint.get_by_fingerprint(fingerprint)
        if fp_data:
            user_uuid = fp_data['uuid']
            hmac_key = fp_data['hmac_key']
        else:
            # Create new fingerprint
            user_uuid = str(uuid.uuid4())
            hmac_key = uuid.uuid4().hex
            try:
                Fingerprint.create(user_uuid, fingerprint, hmac_key, browser_data)
            except Exception as e:
                return jsonify({'success': False, 'error': 'Failed to create fingerprint'}), 500

        # Create signature
        sig = hmac.new(hmac_key.encode(), f"{user_uuid}:{fingerprint}".encode(), hashlib.sha256).hexdigest()

        resp = make_response(jsonify({'success': True}))
        resp.set_cookie('uuid', user_uuid, httponly=True, max_age=30*24*60*60)  # 30 days
        resp.set_cookie('fp', fingerprint, httponly=True, max_age=30*24*60*60)
        resp.set_cookie('sig', sig, httponly=True, max_age=30*24*60*60)
        return resp

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@views.route('/login', methods=['GET', 'POST'])
def login():
    """User login page"""
    if request.method == 'POST':
        try:
            username = sanitize_input(request.form.get('username'))
            password = request.form.get('password')

            # Validate input
            if not username or not password:
                flash('Username and password are required', 'error')
                return render_template('auth/login.html')

            user = User.get_by_username(username)
            if user and User.check_password(user, password) and user.get('is_active'):
                # Update last login
                User.update_last_login(user['id'])

                # Set session
                session['user_id'] = user['id']
                session['username'] = user['username']
                session['role'] = user['role']
                session.permanent = True

                # Log successful login
                LoginLog.create(
                    user_id=user['id'],
                    username=user['username'],
                    success=True,
                    ip_address=get_client_ip(),
                    user_agent=request.headers.get('User-Agent', '')
                )

                flash(f'Welcome back, {user["username"]}!', 'success')

                # Redirect based on role
                if User.is_admin(user):
                    return redirect(url_for('views.admin_dashboard'))
                else:
                    return redirect(url_for('views.publisher_dashboard'))
            else:
                # Log failed login
                LoginLog.create(
                    user_id=None,
                    username=username or 'Unknown',
                    success=False,
                    ip_address=get_client_ip(),
                    user_agent=request.headers.get('User-Agent', ''),
                    failure_reason='Invalid credentials or inactive account'
                )
                flash('Invalid credentials or account is inactive', 'error')

        except Exception as e:
            flash(f'Login error: {str(e)}', 'error')

    return render_template('auth/login.html')

@views.route('/logout')
def logout():
    """User logout"""
    session.clear()
    flash('You have been logged out', 'info')
    return redirect(url_for('views.index'))

@views.route('/app/<int:app_id>')
def app_detail(app_id):
    """App detail page"""
    try:
        if app_id <= 0:
            abort(404)

        app = App.get_by_id(app_id)
        if not app:
            abort(404)

        screenshots = Screenshot.get_by_app_id(app_id)
        ratings = AppRating.get_by_app_id(app_id, limit=10)  # Limit to recent 10 ratings

        # Calculate rating stats
        avg_rating = App.get_average_rating(app_id)
        rating_count = App.get_rating_count(app_id)
        rating_distribution = AppRating.get_rating_distribution(app_id)

        # Check if current user has rated this app and can edit
        user_rating_id = None
        can_edit_rating = False
        edit_time_remaining = None

        if session.get('user_id'):
            user_rating_id = AppRating.user_has_rated(app_id, session['user_id'])
        else:
            # Check by IP for anonymous users
            user_rating_id = AppRating.user_has_rated(app_id, ip_address=get_client_ip())

        if user_rating_id:
            # Check if user can edit their rating
            can_edit, message = AppRating.can_edit_rating(
                user_rating_id,
                session.get('user_id'),
                get_client_ip()
            )
            can_edit_rating = can_edit

            # Calculate time remaining for editing
            if can_edit:
                with get_db() as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT timestamp FROM app_ratings WHERE id = ?', (user_rating_id,))
                    row = cursor.fetchone()
                    if row:
                        timestamp_str = row[0]
                        try:
                            if isinstance(timestamp_str, str):
                                timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                            else:
                                timestamp = timestamp_str

                            time_limit = timestamp + timedelta(hours=AppRating.EDIT_TIME_LIMIT_HOURS)
                            remaining = time_limit - datetime.now()
                            if remaining.total_seconds() > 0:
                                edit_time_remaining = {
                                    'hours': int(remaining.total_seconds() // 3600),
                                    'minutes': int((remaining.total_seconds() % 3600) // 60)
                                }
                        except:
                            pass

        return render_template('app_detail.html',
                             app=app,
                             screenshots=screenshots,
                             ratings=ratings,
                             avg_rating=avg_rating,
                             rating_count=rating_count,
                             rating_distribution=rating_distribution,
                             user_has_rated=bool(user_rating_id),
                             user_rating_id=user_rating_id,
                             can_edit_rating=can_edit_rating,
                             edit_time_remaining=edit_time_remaining)
    except Exception as e:
        flash(f'Error loading app details: {str(e)}', 'error')
        return redirect(url_for('views.index'))

@views.route('/download/<int:app_id>')
def download_app(app_id):
    """Download app file"""
    try:
        app = App.get_by_id(app_id)
        if not app:
            abort(404)

        # Log download
        DownloadLog.create(
            app_id=app_id,
            ip_address=get_client_ip(),
            user_agent=request.headers.get('User-Agent', '')
        )

        # Increment download count
        App.increment_downloads(app_id)

        # Check if app is paid - redirect to external link
        if App.is_paid_app(app):
            external_url = app.get('external_url')
            if external_url:
                # Log download attempt as external redirect
                DownloadLog.create(
                    app_id=app_id,
                    ip_address=get_client_ip(),
                    user_agent=request.headers.get('User-Agent', ''),
                    download_type='external_redirect'
                )

                # Redirect to external purchase/download link
                return redirect(external_url)
            else:
                flash('This is a paid app but no purchase link is available. Please contact the developer.', 'warning')
                return redirect(url_for('views.app_detail', app_id=app_id))

        # Handle external URL (for free apps)
        if app.get('external_url') and not app.get('file_path'):
            return redirect(app['external_url'])

        # Handle local file
        if app.get('file_path'):
            # Construct proper file path
            file_path = os.path.join(Config.UPLOAD_FOLDER, app['file_path'])

            if os.path.exists(file_path):
                # Get original filename for download
                original_filename = os.path.basename(app['file_path'])
                # Remove timestamp prefix if present
                if '_' in original_filename and len(original_filename.split('_')[0]) == 15:
                    original_filename = '_'.join(original_filename.split('_')[2:])

                return send_file(
                    file_path,
                    as_attachment=True,
                    download_name=original_filename or f"{app['name']}.zip"
                )
            else:
                flash('File not found on server', 'error')
        else:
            flash('No download available for this app', 'error')

    except Exception as e:
        flash(f'Download error: {str(e)}', 'error')

    return redirect(url_for('views.app_detail', app_id=app_id))

# Admin routes
@views.route('/admin')
def admin_dashboard():
    """Admin dashboard"""
    auth_check = require_admin()
    if auth_check:
        return auth_check

    try:
        # Get comprehensive stats
        total_apps = App.get_count()
        total_users = len(User.get_all())
        featured_apps = len(App.get_featured())
        categories = App.get_categories()

        # Get recent activity
        recent_apps = App.get_all(limit=5)
        recent_logs = AdminLog.get_all(limit=10)

        # Get download stats
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT SUM(downloads) FROM apps')
            total_downloads = cursor.fetchone()[0] or 0

            cursor.execute('SELECT COUNT(*) FROM download_logs WHERE DATE(timestamp) = DATE("now")')
            today_downloads = cursor.fetchone()[0] or 0

        stats = {
            'total_apps': total_apps,
            'total_users': total_users,
            'featured_apps': featured_apps,
            'total_categories': len(categories),
            'total_downloads': total_downloads,
            'today_downloads': today_downloads
        }

        return render_template('admin/dashboard.html',
                             stats=stats,
                             recent_apps=recent_apps,
                             recent_logs=recent_logs,
                             categories=categories)
    except Exception as e:
        flash(f'Error loading dashboard: {str(e)}', 'error')
        return redirect(url_for('views.index'))

@views.route('/admin/apps')
def admin_apps():
    """Admin apps management"""
    auth_check = require_admin()
    if auth_check:
        return auth_check

    try:
        apps_raw = App.get_all()

        # Create pagination-like object for template compatibility
        class AppList:
            def __init__(self, items):
                self.items = items
                self.total = len(items)
                self.pages = max(1, (len(items) + 49) // 50)  # 50 items per page
                self.page = 1
                self.per_page = len(items)
                self.has_prev = False
                self.has_next = False
                self.prev_num = 1
                self.next_num = 1

            def iter_pages(self):
                return [1]

        apps = AppList(apps_raw)
        return render_template('admin/apps.html', apps=apps)
    except Exception as e:
        flash(f'Error loading apps: {str(e)}', 'error')
        return redirect(url_for('views.admin_dashboard'))

@views.route('/admin/users')
def admin_users():
    """Admin users management"""
    auth_check = require_admin()
    if auth_check:
        return auth_check

    try:
        users = User.get_all()
        return render_template('admin/users.html', users=users)
    except Exception as e:
        flash(f'Error loading users: {str(e)}', 'error')
        return redirect(url_for('views.admin_dashboard'))

@views.route('/admin/logs')
def admin_logs():
    """Admin logs"""
    auth_check = require_admin()
    if auth_check:
        return auth_check

    try:
        log_type = request.args.get('type', 'admin')

        # Select logs based on type
        if log_type == 'admin':
            logs_raw = AdminLog.get_all(limit=100)
        elif log_type == 'download':
            logs_raw = DownloadLog.get_all(limit=100)
        elif log_type == 'login':
            # Get login logs
            logs_raw = LoginLog.get_all(limit=100)
        else:
            logs_raw = AdminLog.get_all(limit=100)  # fallback to admin logs

        # Convert timestamp strings to datetime objects for template compatibility
        for log in logs_raw:
            if isinstance(log.get('timestamp'), str):
                try:
                    # Try different timestamp formats
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f']:
                        try:
                            log['timestamp'] = datetime.strptime(log['timestamp'], fmt)
                            break
                        except ValueError:
                            continue
                except Exception:
                    # If all parsing fails, keep original string
                    pass

        class LogList:
            def __init__(self, items):
                self.items = items
                self.total = len(items)
                self.pages = max(1, (len(items) + 49) // 50)  # 50 items per page
                self.page = 1
                self.per_page = len(items)
                self.has_prev = False
                self.has_next = False
                self.prev_num = 1
                self.next_num = 1

            def iter_pages(self):
                return [1]

        logs = LogList(logs_raw)
        return render_template('admin/logs.html', logs=logs, log_type=log_type)

    except Exception as e:
        flash(f'Error loading logs: {str(e)}', 'error')
        return redirect(url_for('views.admin_dashboard'))

@views.route('/admin/add_app', methods=['GET', 'POST'])
def admin_add_app():
    """Add new app"""
    auth_check = require_admin()
    if auth_check:
        return auth_check

    if request.method == 'POST':
        try:
            # Get and validate form data
            name = sanitize_input(request.form.get('name'))
            description = sanitize_input(request.form.get('description'))
            short_description = sanitize_input(request.form.get('short_description'))
            version = sanitize_input(request.form.get('version'))
            developer = sanitize_input(request.form.get('developer'))
            category = sanitize_input(request.form.get('category'))
            price = validate_numeric_input(request.form.get('price'), 0, 0)
            external_url = sanitize_input(request.form.get('external_url'))
            is_featured = bool(request.form.get('is_featured'))

            # Validate required fields
            if not all([name, description, short_description, version, developer, category]):
                flash('All required fields must be filled', 'error')
                return render_template('admin/add_app.html', categories=Config.CATEGORIES)

            # Handle file upload
            file_path = None
            file_size = 0
            app_file = request.files.get('app_file')

            if app_file and app_file.filename:
                file_path, error = handle_file_upload(app_file, 'apps')
                if error:
                    flash(f'File upload error: {error}', 'error')
                    return render_template('admin/add_app.html', categories=Config.CATEGORIES)

                # Get file size
                full_file_path = os.path.join(Config.UPLOAD_FOLDER, file_path)
                if os.path.exists(full_file_path):
                    file_size = os.path.getsize(full_file_path)

            # Validate that either file or external URL is provided
            if not file_path and not external_url:
                flash('Either upload a file or provide an external download URL', 'error')
                return render_template('admin/add_app.html', categories=Config.CATEGORIES)

            # Create app
            app_id = App.create(
                name=name,
                description=description,
                short_description=short_description,
                version=version,
                developer=developer,
                category=category,
                user_id=session['user_id'],
                uploaded_by=session['username'],
                price=price,
                file_path=file_path,
                external_url=external_url,
                file_size=file_size,
                is_featured=is_featured
            )

            if app_id:
                # Log action
                log_admin_action(
                    session['user_id'],
                    session['username'],
                    'add_app',
                    f'Added app: {name} (ID: {app_id})'
                )

                flash('App added successfully', 'success')
                return redirect(url_for('views.admin_apps'))
            else:
                flash('Failed to create app', 'error')

        except Exception as e:
            flash(f'Error creating app: {str(e)}', 'error')

    return render_template('admin/add_app.html', categories=Config.CATEGORIES)

@views.route('/admin/edit_app/<int:app_id>', methods=['GET', 'POST'])
def admin_edit_app(app_id):
    """Edit app"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    app = App.get_by_id(app_id)
    if not app:
        abort(404)

    if request.method == 'POST':
        # Update app
        update_data = {
            'name': sanitize_input(request.form.get('name')),
            'description': sanitize_input(request.form.get('description')),
            'short_description': sanitize_input(request.form.get('short_description')),
            'version': sanitize_input(request.form.get('version')),
            'developer': sanitize_input(request.form.get('developer')),
            'category': sanitize_input(request.form.get('category')),
            'price': float(request.form.get('price', 0)),
            'external_url': sanitize_input(request.form.get('external_url')),
            'is_featured': bool(request.form.get('is_featured'))
        }

        App.update(app_id, **update_data)

        # Log action
        log_admin_action(
            session['user_id'],
            session['username'],
            'edit_app',
            f'Edited app: {update_data["name"]} (ID: {app_id})'
        )

        flash('App updated successfully', 'success')
        return redirect(url_for('views.admin_apps'))

    return render_template('admin/edit_app.html', app=app, categories=Config.CATEGORIES)

@views.route('/admin/delete_app/<int:app_id>', methods=['POST'])
def admin_delete_app(app_id):
    """Delete app"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    app = App.get_by_id(app_id)
    if not app:
        abort(404)

    app_name = app['name']
    App.delete(app_id)

    # Log action
    log_admin_action(
        session['user_id'],
        session['username'],
        'delete_app',
        f'Deleted app: {app_name} (ID: {app_id})'
    )

    flash('App deleted successfully', 'success')
    return redirect(url_for('views.admin_apps'))

# Publisher routes
@views.route('/publisher')
def publisher_dashboard():
    """Publisher dashboard"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        user_id = session['user_id']

        # Get user's apps
        all_apps = App.get_all()
        user_apps = [app for app in all_apps if app['user_id'] == user_id]

        # Get user statistics
        user_stats = User.get_stats(user_id)

        # Get recent activity (last 5 apps)
        recent_apps = user_apps[:5] if user_apps else []

        return render_template('publisher/dashboard.html',
                             apps=user_apps,
                             user_stats=user_stats,
                             recent_apps=recent_apps)
    except Exception as e:
        flash(f'Error loading dashboard: {str(e)}', 'error')
        return redirect(url_for('views.index'))

@views.route('/publisher/add_app', methods=['GET', 'POST'])
def publisher_add_app():
    """Publisher add app"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    if request.method == 'POST':
        try:
            # Get and validate form data
            name = sanitize_input(request.form.get('name'))
            description = sanitize_input(request.form.get('description'))
            short_description = sanitize_input(request.form.get('short_description'))
            version = sanitize_input(request.form.get('version'))
            developer = sanitize_input(request.form.get('developer'))
            category = sanitize_input(request.form.get('category'))
            price = validate_numeric_input(request.form.get('price'), 0, 0)
            external_url = sanitize_input(request.form.get('external_url'))

            # Validate required fields
            if not all([name, description, short_description, version, developer, category]):
                flash('All required fields must be filled', 'error')
                return render_template('publisher/add_app.html', categories=Config.CATEGORIES)

            # Handle file upload
            file_path = None
            file_size = 0
            app_file = request.files.get('app_file')

            if app_file and app_file.filename:
                file_path, error = handle_file_upload(app_file, 'apps')
                if error:
                    flash(f'File upload error: {error}', 'error')
                    return render_template('publisher/add_app.html', categories=Config.CATEGORIES)

                # Get file size
                full_file_path = os.path.join(Config.UPLOAD_FOLDER, file_path)
                if os.path.exists(full_file_path):
                    file_size = os.path.getsize(full_file_path)

            # Validate that either file or external URL is provided
            if not file_path and not external_url:
                flash('Either upload a file or provide an external download URL', 'error')
                return render_template('publisher/add_app.html', categories=Config.CATEGORIES)

            # Create app
            app_id = App.create(
                name=name,
                description=description,
                short_description=short_description,
                version=version,
                developer=developer,
                category=category,
                user_id=session['user_id'],
                uploaded_by=session['username'],
                price=price,
                file_path=file_path,
                external_url=external_url,
                file_size=file_size
            )

            if app_id:
                flash('App submitted successfully', 'success')
                return redirect(url_for('views.publisher_dashboard'))
            else:
                flash('Failed to create app', 'error')

        except Exception as e:
            flash(f'Error creating app: {str(e)}', 'error')

    return render_template('publisher/add_app.html', categories=Config.CATEGORIES)

@views.route('/publisher/edit_app/<int:app_id>', methods=['GET', 'POST'])
def publisher_edit_app(app_id):
    """Publisher edit app"""
    if not session.get('user_id'):
        flash('Please log in', 'error')
        return redirect(url_for('views.login'))

    app = App.get_by_id(app_id)
    if not app or app['user_id'] != session['user_id']:
        flash('Access denied', 'error')
        return redirect(url_for('views.publisher_dashboard'))

    if request.method == 'POST':
        # Update app
        update_data = {
            'name': sanitize_input(request.form.get('name')),
            'description': sanitize_input(request.form.get('description')),
            'short_description': sanitize_input(request.form.get('short_description')),
            'version': sanitize_input(request.form.get('version')),
            'developer': sanitize_input(request.form.get('developer')),
            'category': sanitize_input(request.form.get('category')),
            'price': float(request.form.get('price', 0)),
            'external_url': sanitize_input(request.form.get('external_url'))
        }

        App.update(app_id, **update_data)
        flash('App updated successfully', 'success')
        return redirect(url_for('views.publisher_dashboard'))

    return render_template('publisher/edit_app.html', app=app, categories=Config.CATEGORIES)

@views.route('/upload_screenshot/<int:app_id>', methods=['POST'])
def upload_screenshot(app_id):
    """Upload screenshot for an app"""
    auth_check = require_login()
    if auth_check:
        return auth_check

    try:
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        # Check if user owns the app or is admin
        if app['user_id'] != session['user_id'] and session.get('role') != 'admin':
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        screenshot_file = request.files.get('screenshot')
        if not screenshot_file or screenshot_file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Check if it's an image file
        allowed_image_extensions = {'png', 'jpg', 'jpeg', 'gif'}
        if not ('.' in screenshot_file.filename and
                screenshot_file.filename.rsplit('.', 1)[1].lower() in allowed_image_extensions):
            return jsonify({'success': False, 'error': 'Only image files are allowed'}), 400

        # Upload screenshot
        file_path, error = handle_file_upload(screenshot_file, 'screenshots')
        if error:
            return jsonify({'success': False, 'error': error}), 500

        # Save to database
        caption = request.form.get('caption', '').strip()
        screenshot_id = Screenshot.create(app_id, file_path, caption)

        if screenshot_id:
            return jsonify({'success': True, 'screenshot_id': screenshot_id, 'message': 'Screenshot uploaded successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to save screenshot'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@views.route('/app/<int:app_id>/rate', methods=['POST'])
def rate_app(app_id):
    """Rate an app or edit existing rating (form submission)"""
    try:
        if app_id <= 0:
            flash('Invalid app ID', 'error')
            return redirect(url_for('views.app_detail', app_id=app_id))

        rating = request.form.get('rating')
        review = sanitize_input(request.form.get('review', ''))
        edit_reason = sanitize_input(request.form.get('edit_reason', ''))
        is_edit = request.form.get('is_edit') == 'true'

        # Validate rating
        try:
            rating = int(rating)
            if rating < 1 or rating > 5:
                flash('Rating must be between 1 and 5', 'error')
                return redirect(url_for('views.app_detail', app_id=app_id))
        except (ValueError, TypeError):
            flash('Invalid rating value', 'error')
            return redirect(url_for('views.app_detail', app_id=app_id))

        # Check if app exists
        app = App.get_by_id(app_id)
        if not app:
            flash('App not found', 'error')
            return redirect(url_for('views.index'))

        # Check if user has already rated this app
        user_rating_id = None
        if session.get('user_id'):
            user_rating_id = AppRating.user_has_rated(app_id, session['user_id'])
        else:
            # Check by IP for anonymous users
            user_rating_id = AppRating.user_has_rated(app_id, ip_address=get_client_ip())

        if is_edit and user_rating_id:
            # Update existing rating
            success, message = AppRating.update_rating(
                user_rating_id,
                rating,
                review,
                session.get('user_id'),
                get_client_ip(),
                edit_reason
            )

            if success:
                flash('Your rating has been updated!', 'success')
            else:
                flash(f'Failed to update rating: {message}', 'error')

        elif not user_rating_id:
            # Create new rating
            rating_id = AppRating.create(
                app_id=app_id,
                rating=rating,
                review=review,
                user_id=session.get('user_id'),
                ip_address=get_client_ip()
            )

            if rating_id:
                flash('Thank you for your rating!', 'success')
            else:
                flash('Failed to submit rating', 'error')
        else:
            flash('You have already rated this app', 'warning')

        return redirect(url_for('views.app_detail', app_id=app_id))

    except Exception as e:
        flash(f'Error submitting rating: {str(e)}', 'error')
        return redirect(url_for('views.app_detail', app_id=app_id))

@views.route('/uploads/<path:filename>')
def uploaded_file(filename):
    """Serve uploaded files (icons, screenshots, etc.)"""
    try:
        file_path = os.path.join(Config.UPLOAD_FOLDER, filename)

        # Security check - ensure file is within upload directory
        if not os.path.abspath(file_path).startswith(os.path.abspath(Config.UPLOAD_FOLDER)):
            abort(403)

        if os.path.exists(file_path):
            return send_file(file_path)
        else:
            abort(404)

    except Exception as e:
        abort(404)

@views.route('/app/<int:app_id>/report', methods=['POST'])
def report_abuse(app_id):
    """Report abuse for an app"""
    try:
        if app_id <= 0:
            return jsonify({'success': False, 'error': 'Invalid app ID'}), 400

        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        report_type = sanitize_input(data.get('report_type', ''))
        reason = sanitize_input(data.get('reason', ''))
        description = sanitize_input(data.get('description', ''))

        # Validate required fields
        if not report_type or not reason:
            return jsonify({'success': False, 'error': 'Report type and reason are required'}), 400

        # Check if app exists
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        # Create abuse report
        report_id = AbuseReport.create(
            app_id=app_id,
            report_type=report_type,
            reason=reason,
            description=description,
            user_id=session.get('user_id'),
            ip_address=get_client_ip(),
            user_agent=request.headers.get('User-Agent', '')
        )

        if report_id:
            return jsonify({
                'success': True,
                'message': 'Report submitted successfully. Thank you for helping keep our platform safe.',
                'report_id': report_id
            })
        else:
            return jsonify({'success': False, 'error': 'Failed to submit report'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@views.route('/suggestions', methods=['GET', 'POST'])
def suggestions():
    """Handle suggestions"""
    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            category = sanitize_input(data.get('category', ''))
            title = sanitize_input(data.get('title', ''))
            description = sanitize_input(data.get('description', ''))
            priority = sanitize_input(data.get('priority', 'normal'))

            # Validate required fields
            if not category or not title or not description:
                return jsonify({'success': False, 'error': 'Category, title, and description are required'}), 400

            # Validate category
            valid_categories = Suggestion.get_categories()
            if category not in valid_categories:
                return jsonify({'success': False, 'error': 'Invalid category'}), 400

            # Create suggestion
            suggestion_id = Suggestion.create(
                category=category,
                title=title,
                description=description,
                user_id=session.get('user_id'),
                ip_address=get_client_ip(),
                user_agent=request.headers.get('User-Agent', ''),
                priority=priority
            )

            if suggestion_id:
                return jsonify({
                    'success': True,
                    'message': 'Suggestion submitted successfully. Thank you for your feedback!',
                    'suggestion_id': suggestion_id
                })
            else:
                return jsonify({'success': False, 'error': 'Failed to submit suggestion'}), 500

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    else:
        # GET request - show suggestions page
        try:
            categories = Suggestion.get_categories()
            return render_template('suggestions.html', categories=categories)
        except Exception as e:
            flash(f'Error loading suggestions page: {str(e)}', 'error')
            return redirect(url_for('views.index'))

# Shortlink routes
@views.route('/s/<short_code>')
def shortlink_redirect(short_code):
    """Redirect shortlink"""
    try:
        shortlink = Shortlink.get_by_code(short_code)
        if not shortlink:
            abort(404)

        # Check if expired
        if shortlink.get('expires_at'):
            try:
                expires_at = datetime.strptime(shortlink['expires_at'], '%Y-%m-%d %H:%M:%S')
                if datetime.now() > expires_at:
                    abort(404)
            except:
                pass

        # Record click
        Shortlink.increment_clicks(
            shortlink['id'],
            get_client_ip(),
            request.headers.get('User-Agent', ''),
            request.headers.get('Referer', '')
        )

        # Redirect to original URL
        return redirect(shortlink['original_url'])

    except Exception as e:
        abort(404)

@views.route('/shortlinks', methods=['GET', 'POST'])
def manage_shortlinks():
    """Manage shortlinks (admin/publisher only)"""
    if not session.get('user_id'):
        flash('Please log in', 'error')
        return redirect(url_for('views.login'))

    user = User.get_by_id(session['user_id'])
    if not user or user['role'] not in ['admin', 'publisher']:
        flash('Access denied', 'error')
        return redirect(url_for('views.index'))

    if request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({'success': False, 'error': 'No data provided'}), 400

            original_url = sanitize_input(data.get('original_url', ''))
            title = sanitize_input(data.get('title', ''))
            description = sanitize_input(data.get('description', ''))
            custom_code = sanitize_input(data.get('custom_code', ''))
            app_id = data.get('app_id')

            if not original_url:
                return jsonify({'success': False, 'error': 'URL is required'}), 400

            # Validate URL
            if not original_url.startswith(('http://', 'https://')):
                original_url = 'https://' + original_url

            # Create shortlink
            shortlink_id, short_code = Shortlink.create(
                original_url=original_url,
                user_id=session['user_id'],
                app_id=app_id,
                title=title,
                description=description,
                custom_code=custom_code if custom_code else None
            )

            if shortlink_id:
                return jsonify({
                    'success': True,
                    'message': 'Shortlink created successfully',
                    'shortlink_id': shortlink_id,
                    'short_code': short_code,
                    'short_url': f"{request.host_url}s/{short_code}"
                })
            else:
                return jsonify({'success': False, 'error': short_code}), 400

        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 500

    else:
        # GET request - show shortlinks page
        try:
            shortlinks = Shortlink.get_by_user(session['user_id'])
            user_apps = App.get_by_user(session['user_id']) if user['role'] == 'publisher' else []

            return render_template('shortlinks.html',
                                 shortlinks=shortlinks,
                                 user_apps=user_apps,
                                 user_role=user['role'])
        except Exception as e:
            flash(f'Error loading shortlinks: {str(e)}', 'error')
            return redirect(url_for('views.index'))

# Publisher post management routes
@views.route('/publisher/posts')
def publisher_posts():
    """Publisher posts management"""
    if not session.get('user_id'):
        flash('Please log in', 'error')
        return redirect(url_for('views.login'))

    user = User.get_by_id(session['user_id'])
    if not user or user['role'] != 'publisher':
        flash('Access denied', 'error')
        return redirect(url_for('views.index'))

    try:
        posts = Post.get_by_user(session['user_id'])
        return render_template('publisher/posts.html', posts=posts)
    except Exception as e:
        flash(f'Error loading posts: {str(e)}', 'error')
        return redirect(url_for('views.publisher_dashboard'))

@views.route('/publisher/posts/add', methods=['GET', 'POST'])
def publisher_add_post():
    """Add new post"""
    if not session.get('user_id'):
        flash('Please log in', 'error')
        return redirect(url_for('views.login'))

    user = User.get_by_id(session['user_id'])
    if not user or user['role'] != 'publisher':
        flash('Access denied', 'error')
        return redirect(url_for('views.index'))

    if request.method == 'POST':
        try:
            title = sanitize_input(request.form.get('title', ''))
            content = sanitize_input(request.form.get('content', ''))
            excerpt = sanitize_input(request.form.get('excerpt', ''))
            category = sanitize_input(request.form.get('category', 'general'))
            tags = sanitize_input(request.form.get('tags', ''))
            status = sanitize_input(request.form.get('status', 'draft'))

            if not title or not content:
                flash('Title and content are required', 'error')
                return render_template('publisher/add_post.html', categories=Post.get_categories())

            post_id = Post.create(
                user_id=session['user_id'],
                title=title,
                content=content,
                excerpt=excerpt,
                category=category,
                tags=tags,
                status=status
            )

            if post_id:
                flash('Post created successfully', 'success')
                return redirect(url_for('views.publisher_posts'))
            else:
                flash('Failed to create post', 'error')

        except Exception as e:
            flash(f'Error creating post: {str(e)}', 'error')

    return render_template('publisher/add_post.html', categories=Post.get_categories())

@views.route('/publisher/posts/edit/<int:post_id>', methods=['GET', 'POST'])
def publisher_edit_post(post_id):
    """Edit post"""
    if not session.get('user_id'):
        flash('Please log in', 'error')
        return redirect(url_for('views.login'))

    user = User.get_by_id(session['user_id'])
    if not user or user['role'] != 'publisher':
        flash('Access denied', 'error')
        return redirect(url_for('views.index'))

    post = Post.get_by_id(post_id)
    if not post or post['user_id'] != session['user_id']:
        flash('Post not found or access denied', 'error')
        return redirect(url_for('views.publisher_posts'))

    if request.method == 'POST':
        try:
            title = sanitize_input(request.form.get('title', ''))
            content = sanitize_input(request.form.get('content', ''))
            excerpt = sanitize_input(request.form.get('excerpt', ''))
            category = sanitize_input(request.form.get('category', 'general'))
            tags = sanitize_input(request.form.get('tags', ''))
            status = sanitize_input(request.form.get('status', 'draft'))

            if not title or not content:
                flash('Title and content are required', 'error')
                return render_template('publisher/edit_post.html', post=post, categories=Post.get_categories())

            success = Post.update(
                post_id=post_id,
                user_id=session['user_id'],
                title=title,
                content=content,
                excerpt=excerpt,
                category=category,
                tags=tags,
                status=status
            )

            if success:
                flash('Post updated successfully', 'success')
                return redirect(url_for('views.publisher_posts'))
            else:
                flash('Failed to update post', 'error')

        except Exception as e:
            flash(f'Error updating post: {str(e)}', 'error')

    return render_template('publisher/edit_post.html', post=post, categories=Post.get_categories())

@views.route('/publisher/posts/delete/<int:post_id>', methods=['POST'])
def publisher_delete_post(post_id):
    """Delete post"""
    if not session.get('user_id'):
        return jsonify({'success': False, 'error': 'Not logged in'}), 401

    user = User.get_by_id(session['user_id'])
    if not user or user['role'] != 'publisher':
        return jsonify({'success': False, 'error': 'Access denied'}), 403

    try:
        success = Post.delete(post_id, session['user_id'])
        if success:
            return jsonify({'success': True, 'message': 'Post deleted successfully'})
        else:
            return jsonify({'success': False, 'error': 'Failed to delete post'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# Media editing routes
@views.route('/app/<int:app_id>/update_icon', methods=['POST'])
def update_app_icon(app_id):
    """Update app icon"""
    if not session.get('user_id'):
        return jsonify({'success': False, 'error': 'Not logged in'}), 401

    try:
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        # Check ownership
        if app['user_id'] != session['user_id']:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        if 'icon' not in request.files:
            return jsonify({'success': False, 'error': 'No icon file provided'}), 400

        file = request.files['icon']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'success': False, 'error': 'Invalid file type. Use PNG, JPG, JPEG, GIF, or WebP'}), 400

        # Generate unique filename
        filename = f"{uuid.uuid4().hex}_{file.filename}"
        icon_path = os.path.join(Config.UPLOAD_FOLDER, 'icons', filename)

        # Create icons directory if it doesn't exist
        os.makedirs(os.path.dirname(icon_path), exist_ok=True)

        # Save file
        file.save(icon_path)

        # Update database
        success, message = App.update_icon(app_id, f"icons/{filename}", session['user_id'])

        if success:
            return jsonify({
                'success': True,
                'message': message,
                'icon_url': f"/uploads/icons/{filename}"
            })
        else:
            # Clean up file if database update failed
            if os.path.exists(icon_path):
                os.remove(icon_path)
            return jsonify({'success': False, 'error': message}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@views.route('/app/<int:app_id>/delete_icon', methods=['POST'])
def delete_app_icon(app_id):
    """Delete app icon"""
    if not session.get('user_id'):
        return jsonify({'success': False, 'error': 'Not logged in'}), 401

    try:
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        # Check ownership
        if app['user_id'] != session['user_id']:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        success, message = App.delete_icon(app_id, session['user_id'])

        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'error': message}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@views.route('/screenshot/<int:screenshot_id>/update', methods=['POST'])
def update_screenshot(screenshot_id):
    """Update screenshot caption or reorder"""
    if not session.get('user_id'):
        return jsonify({'success': False, 'error': 'Not logged in'}), 401

    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'error': 'No data provided'}), 400

        action = data.get('action')

        if action == 'update_caption':
            caption = sanitize_input(data.get('caption', ''))
            success, message = Screenshot.update_caption(screenshot_id, caption, session['user_id'])
        elif action == 'reorder':
            new_order = data.get('order')
            if new_order is None:
                return jsonify({'success': False, 'error': 'Order is required'}), 400
            success, message = Screenshot.reorder(screenshot_id, new_order, session['user_id'])
        else:
            return jsonify({'success': False, 'error': 'Invalid action'}), 400

        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'error': message}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@views.route('/screenshot/<int:screenshot_id>/delete', methods=['POST'])
def delete_screenshot(screenshot_id):
    """Delete screenshot"""
    if not session.get('user_id'):
        return jsonify({'success': False, 'error': 'Not logged in'}), 401

    try:
        success, message = Screenshot.delete(screenshot_id, session['user_id'])

        if success:
            return jsonify({'success': True, 'message': message})
        else:
            return jsonify({'success': False, 'error': message}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@views.route('/app/<int:app_id>/add_screenshot', methods=['POST'])
def add_screenshot(app_id):
    """Add new screenshot"""
    if not session.get('user_id'):
        return jsonify({'success': False, 'error': 'Not logged in'}), 401

    try:
        app = App.get_by_id(app_id)
        if not app:
            return jsonify({'success': False, 'error': 'App not found'}), 404

        # Check ownership
        if app['user_id'] != session['user_id']:
            return jsonify({'success': False, 'error': 'Access denied'}), 403

        if 'screenshot' not in request.files:
            return jsonify({'success': False, 'error': 'No screenshot file provided'}), 400

        file = request.files['screenshot']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'success': False, 'error': 'Invalid file type. Use PNG, JPG, JPEG, GIF, or WebP'}), 400

        # Generate unique filename
        filename = f"{uuid.uuid4().hex}_{file.filename}"
        screenshot_path = os.path.join(Config.UPLOAD_FOLDER, 'screenshots', filename)

        # Create screenshots directory if it doesn't exist
        os.makedirs(os.path.dirname(screenshot_path), exist_ok=True)

        # Save file
        file.save(screenshot_path)

        # Get caption from form
        caption = sanitize_input(request.form.get('caption', ''))

        # Create screenshot record
        screenshot_id = Screenshot.create(
            app_id=app_id,
            file_path=f"screenshots/{filename}",
            caption=caption
        )

        if screenshot_id:
            return jsonify({
                'success': True,
                'message': 'Screenshot added successfully',
                'screenshot_id': screenshot_id,
                'screenshot_url': f"/uploads/screenshots/{filename}"
            })
        else:
            # Clean up file if database insert failed
            if os.path.exists(screenshot_path):
                os.remove(screenshot_path)
            return jsonify({'success': False, 'error': 'Failed to save screenshot'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@views.route('/test_filters')
def test_filters():
    """Test route to verify template filters work"""
    # Create a test app dict
    test_app = {
        'id': 1,
        'name': 'Test App',
        'file_size': 1048576,  # 1MB
        'file_path': 'test.zip',
        'external_url': None,
        'price': 0
    }

    return f"""
    <h1>Template Filter Test</h1>
    <p>File Size: {App.get_file_size_formatted(test_app['file_size'])}</p>
    <p>Has File: {App.has_file(test_app)}</p>
    <p>Download URL: {App.get_download_url(test_app['id'])}</p>
    <p>Is External: {App.is_external_download(test_app)}</p>
    <p>All filters working correctly!</p>
    """

# User management routes (admin only)
@views.route('/admin/add_user', methods=['GET', 'POST'])
def admin_add_user():
    """Add new user"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    if request.method == 'POST':
        username = sanitize_input(request.form.get('username'))
        email = sanitize_input(request.form.get('email'))
        password = request.form.get('password')
        role = sanitize_input(request.form.get('role'))

        # Check if user exists
        if User.get_by_username(username):
            flash('Username already exists', 'error')
            return render_template('admin/add_user.html')

        if User.get_by_email(email):
            flash('Email already exists', 'error')
            return render_template('admin/add_user.html')

        # Create user
        user_id = User.create(username, email, password, role)

        # Log action
        log_admin_action(
            session['user_id'],
            session['username'],
            'add_user',
            f'Added user: {username} (ID: {user_id})'
        )

        flash('User created successfully', 'success')
        return redirect(url_for('views.admin_users'))

    return render_template('admin/add_user.html')

@views.route('/admin/edit_user/<int:user_id>', methods=['GET', 'POST'])
def admin_edit_user(user_id):
    """Edit user"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    user = User.get_by_id(user_id)
    if not user:
        abort(404)

    if request.method == 'POST':
        update_data = {
            'username': sanitize_input(request.form.get('username')),
            'email': sanitize_input(request.form.get('email')),
            'role': sanitize_input(request.form.get('role')),
            'is_active': bool(request.form.get('is_active'))
        }

        # Add password if provided
        password = request.form.get('password')
        if password and password.strip():
            update_data['password'] = password.strip()

        User.update(user_id, **update_data)

        # Log action
        log_admin_action(
            session['user_id'],
            session['username'],
            'edit_user',
            f'Edited user: {update_data["username"]} (ID: {user_id})'
        )

        flash('User updated successfully', 'success')
        return redirect(url_for('views.admin_users'))

    return render_template('admin/edit_user.html', user=user)

@views.route('/admin/delete_user/<int:user_id>', methods=['POST'])
def admin_delete_user(user_id):
    """Delete user"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    user = User.get_by_id(user_id)
    if not user:
        abort(404)

    # Don't allow deleting yourself
    if user_id == session['user_id']:
        flash('Cannot delete your own account', 'error')
        return redirect(url_for('views.admin_users'))

    username = user['username']
    User.delete(user_id)

    # Log action
    log_admin_action(
        session['user_id'],
        session['username'],
        'delete_user',
        f'Deleted user: {username} (ID: {user_id})'
    )

    flash('User deleted successfully', 'success')
    return redirect(url_for('views.admin_users'))
