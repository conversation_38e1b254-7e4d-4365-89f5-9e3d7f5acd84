from flask import Blueprint, render_template, request, redirect, url_for, flash, session, send_file, jsonify, abort, make_response, Markup
from app.models import User, App, Screenshot, DownloadLog, AdminLog, LoginLog, AppRating, Fingerprint
import bleach
import os
import uuid
import hmac
import hashlib
from datetime import datetime
import markdown
from app.config import Config

views = Blueprint('views', __name__)

@views.app_template_filter('markdown')
def markdown_filter(text):
    """Convert markdown to HTML"""
    html = markdown.markdown(text or '', extensions=['fenced_code', 'codehilite'])
    return Markup(html)

@views.before_app_request
def check_cookie_auth():
    """Simple authentication check - can be simplified or removed"""
    # Allow certain paths without authentication
    allowed_paths = ['/registerf', '/gate', '/static', '/login', '/logout']
    if any(request.path.startswith(path) for path in allowed_paths):
        return

    # For now, let's simplify this - just check if we have basic cookies
    uuid_token = request.cookies.get('uuid')
    fingerprint = request.cookies.get('fp')
    signature = request.cookies.get('sig')

    if not all([uuid_token, fingerprint, signature]):
        return redirect('/gate')

    # Verify the signature
    fp_data = Fingerprint.get_by_uuid(uuid_token)
    if not fp_data or fp_data['fingerprint'] != fingerprint:
        return redirect('/gate')

    hmac_key = fp_data['hmac_key']
    expected_sig = hmac.new(hmac_key.encode(), f"{uuid_token}:{fingerprint}".encode(), hashlib.sha256).hexdigest()
    if not hmac.compare_digest(expected_sig, signature):
        return redirect('/gate')

def sanitize_input(text):
    """Sanitize user input to prevent XSS"""
    if not text:
        return ""
    return bleach.clean(text, tags=[], strip=True)

def get_client_ip():
    """Get client IP address"""
    return request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)

def log_admin_action(user_id, username, action, details=""):
    """Log admin action"""
    AdminLog.create(
        user_id=user_id,
        username=username,
        action=action,
        details=details,
        ip_address=get_client_ip(),
        user_agent=request.headers.get('User-Agent', '')
    )

@views.route('/')
def index():
    """Main page showing apps"""
    page = request.args.get('page', 1, type=int)
    category = request.args.get('category', '')
    search = request.args.get('search', '')

    # Get apps with pagination
    per_page = 12
    offset = (page - 1) * per_page

    # Get all apps (not paginated) for count and for template
    all_apps = App.get_all(category=category, search=search)
    total_apps = len(all_apps)
    # Paginate manually
    apps_page = all_apps[offset:offset+per_page]
    class AppList:
        def __init__(self, items, total):
            self.items = items
            self.total = total
            self.pages = 1
            self.page = page
            self.per_page = per_page
            self.has_prev = has_prev
            self.has_next = has_next
            self.prev_num = max(1, page - 1)
            self.next_num = page + 1 if has_next else page
        def iter_pages(self):
            return [1]
    apps = AppList(apps_page, total_apps)

    featured_apps = [app for app in all_apps if app.get('is_featured')]
    categories = App.get_categories()

    has_prev = page > 1
    has_next = offset + per_page < total_apps

    return render_template('index.html',
                         apps=apps,
                         featured_apps=featured_apps,
                         categories=categories,
                         current_category=category,
                         search_term=search,
                         page=page,
                         has_prev=has_prev,
                         has_next=has_next)

@views.route('/gate')
def gate():
    """Gate page for fingerprinting"""
    return render_template('gate.html')

@views.route('/registerf', methods=['POST'])
def registerf():
    """Register fingerprint"""
    data = request.get_json()
    fingerprint = data.get('fingerprint', '')
    browser_data = str(data.get('browser_data', ''))

    # Check if fingerprint exists
    fp_data = Fingerprint.get_by_fingerprint(fingerprint)
    if fp_data:
        user_uuid = fp_data['uuid']
        hmac_key = fp_data['hmac_key']
    else:
        # Create new fingerprint
        user_uuid = str(uuid.uuid4())
        hmac_key = uuid.uuid4().hex
        Fingerprint.create(user_uuid, fingerprint, hmac_key, browser_data)

    # Create signature
    sig = hmac.new(hmac_key.encode(), f"{user_uuid}:{fingerprint}".encode(), hashlib.sha256).hexdigest()

    resp = make_response(jsonify({'success': True}))
    resp.set_cookie('uuid', user_uuid, httponly=True)
    resp.set_cookie('fp', fingerprint, httponly=True)
    resp.set_cookie('sig', sig, httponly=True)
    return resp

@views.route('/login', methods=['GET', 'POST'])
def login():
    """User login page"""
    if request.method == 'POST':
        username = sanitize_input(request.form.get('username'))
        password = request.form.get('password')

        user = User.get_by_username(username)
        if user and User.check_password(user, password) and user.get('is_active'):
            # Update last login
            User.update_last_login(user['id'])

            # Set session
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['role'] = user['role']
            session.permanent = True

            # Log successful login
            LoginLog.create(
                user_id=user['id'],
                username=user['username'],
                success=True,
                ip_address=get_client_ip(),
                user_agent=request.headers.get('User-Agent', '')
            )

            flash(f'Welcome back, {user["username"]}!', 'success')

            if User.is_admin(user):
                return redirect(url_for('views.admin_dashboard'))
            else:
                return redirect(url_for('views.publisher_dashboard'))
        else:
            # Log failed login
            LoginLog.create(
                user_id=None,
                username=username,
                success=False,
                ip_address=get_client_ip(),
                user_agent=request.headers.get('User-Agent', ''),
                failure_reason='Invalid credentials'
            )
            flash('Invalid credentials', 'error')

    return render_template('auth/login.html')

@views.route('/logout')
def logout():
    """User logout"""
    session.clear()
    flash('You have been logged out', 'info')
    return redirect(url_for('views.index'))

@views.route('/app/<int:app_id>')
def app_detail(app_id):
    """App detail page"""
    app = App.get_by_id(app_id)
    if not app:
        abort(404)

    screenshots = Screenshot.get_by_app_id(app_id)
    ratings = AppRating.get_by_app_id(app_id)

    # Calculate rating stats
    avg_rating = App.get_average_rating(app_id)
    rating_count = App.get_rating_count(app_id)

    return render_template('app_detail.html',
                         app=app,
                         screenshots=screenshots,
                         ratings=ratings,
                         avg_rating=avg_rating,
                         rating_count=rating_count)

@views.route('/download/<int:app_id>')
def download_app(app_id):
    """Download app file"""
    app = App.get_by_id(app_id)
    if not app:
        abort(404)

    # Log download
    DownloadLog.create(
        app_id=app_id,
        ip_address=get_client_ip(),
        user_agent=request.headers.get('User-Agent', '')
    )

    # Increment download count
    App.increment_downloads(app_id)

    # Handle external URL
    if app.get('external_url'):
        return redirect(app['external_url'])

    # Handle local file
    if app.get('file_path'):
        file_path = os.path.join('app', app['file_path'])
        if os.path.exists(file_path)):
            return send_file(file_path, as_attachment=True)

    flash('Download not available', 'error')
    return redirect(url_for('views.app_detail', app_id=app_id))

# Admin routes
@views.route('/admin')
def admin_dashboard():
    """Admin dashboard"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    # Get stats
    total_apps = len(App.get_all())
    total_users = len(User.get_all())
    recent_apps = App.get_all(limit=5)
    recent_logs = AdminLog.get_all(limit=10)

    return render_template('admin/dashboard.html',
                         total_apps=total_apps,
                         total_users=total_users,
                         recent_apps=recent_apps,
                         recent_logs=recent_logs)

@views.route('/admin/apps')
def admin_apps():
    """Admin apps management"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    apps = App.get_all()
    return render_template('admin/apps.html', apps=apps)

@views.route('/admin/users')
def admin_users():
    """Admin users management"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    users = User.get_all()
    return render_template('admin/users.html', users=users)

@views.route('/admin/logs')
def admin_logs():
    """Admin logs"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    log_type = request.args.get('type', 'activity')
    from datetime import datetime
    # Select logs based on type
    if log_type == 'admin':
        logs_raw = AdminLog.get_all(limit=100)
    elif log_type == 'download':
        logs_raw = DownloadLog.get_all(limit=100)
    else:
        logs_raw = AdminLog.get_all(limit=100)  # fallback to admin logs for now

    # Convert timestamp strings to datetime objects for template compatibility
    for log in logs_raw:
        if isinstance(log.get('timestamp'), str):
            try:
                log['timestamp'] = datetime.strptime(log['timestamp'], '%Y-%m-%d %H:%M:%S')
            except Exception:
                pass

    class LogList:
        def __init__(self, items):
            self.items = items
            self.total = len(items)
            self.pages = 1
            self.page = 1
            self.per_page = len(items)
            self.has_prev = False
            self.has_next = False
            self.prev_num = 1
            self.next_num = 1
        def iter_pages(self):
            return [1]
    logs = LogList(logs_raw)
    return render_template('admin/logs.html', logs=logs, log_type=log_type)

@views.route('/admin/add_app', methods=['GET', 'POST'])
def admin_add_app():
    """Add new app"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    if request.method == 'POST':
        # Get form data
        name = sanitize_input(request.form.get('name'))
        description = sanitize_input(request.form.get('description'))
        short_description = sanitize_input(request.form.get('short_description'))
        version = sanitize_input(request.form.get('version'))
        developer = sanitize_input(request.form.get('developer'))
        category = sanitize_input(request.form.get('category'))
        price = float(request.form.get('price', 0))
        external_url = sanitize_input(request.form.get('external_url'))
        is_featured = bool(request.form.get('is_featured'))

        # Create app
        app_id = App.create(
            name=name,
            description=description,
            short_description=short_description,
            version=version,
            developer=developer,
            category=category,
            user_id=session['user_id'],
            uploaded_by=session['username'],
            price=price,
            external_url=external_url,
            is_featured=is_featured
        )

        # Log action
        log_admin_action(
            session['user_id'],
            session['username'],
            'add_app',
            f'Added app: {name} (ID: {app_id})'
        )

        flash('App added successfully', 'success')
        return redirect(url_for('views.admin_apps'))

    return render_template('admin/add_app.html', categories=Config.CATEGORIES)

@views.route('/admin/edit_app/<int:app_id>', methods=['GET', 'POST'])
def admin_edit_app(app_id):
    """Edit app"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    app = App.get_by_id(app_id)
    if not app:
        abort(404)

    if request.method == 'POST':
        # Update app
        update_data = {
            'name': sanitize_input(request.form.get('name')),
            'description': sanitize_input(request.form.get('description')),
            'short_description': sanitize_input(request.form.get('short_description')),
            'version': sanitize_input(request.form.get('version')),
            'developer': sanitize_input(request.form.get('developer')),
            'category': sanitize_input(request.form.get('category')),
            'price': float(request.form.get('price', 0)),
            'external_url': sanitize_input(request.form.get('external_url')),
            'is_featured': bool(request.form.get('is_featured'))
        }

        App.update(app_id, **update_data)

        # Log action
        log_admin_action(
            session['user_id'],
            session['username'],
            'edit_app',
            f'Edited app: {update_data["name"]} (ID: {app_id})'
        )

        flash('App updated successfully', 'success')
        return redirect(url_for('views.admin_apps'))

    return render_template('admin/edit_app.html', app=app, categories=Config.CATEGORIES)

@views.route('/admin/delete_app/<int:app_id>', methods=['POST'])
def admin_delete_app(app_id):
    """Delete app"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    app = App.get_by_id(app_id)
    if not app:
        abort(404)

    app_name = app['name']
    App.delete(app_id)

    # Log action
    log_admin_action(
        session['user_id'],
        session['username'],
        'delete_app',
        f'Deleted app: {app_name} (ID: {app_id})'
    )

    flash('App deleted successfully', 'success')
    return redirect(url_for('views.admin_apps'))

# Publisher routes
@views.route('/publisher')
def publisher_dashboard():
    """Publisher dashboard"""
    if not session.get('user_id'):
        flash('Please log in', 'error')
        return redirect(url_for('views.login'))

    user_id = session['user_id']
    user_apps = [app for app in App.get_all() if app['user_id'] == user_id]

    return render_template('publisher/dashboard.html', apps=user_apps)

@views.route('/publisher/add_app', methods=['GET', 'POST'])
def publisher_add_app():
    """Publisher add app"""
    if not session.get('user_id'):
        flash('Please log in', 'error')
        return redirect(url_for('views.login'))

    if request.method == 'POST':
        # Get form data
        name = sanitize_input(request.form.get('name'))
        description = sanitize_input(request.form.get('description'))
        short_description = sanitize_input(request.form.get('short_description'))
        version = sanitize_input(request.form.get('version'))
        developer = sanitize_input(request.form.get('developer'))
        category = sanitize_input(request.form.get('category'))
        price = float(request.form.get('price', 0))
        external_url = sanitize_input(request.form.get('external_url'))

        # Create app
        app_id = App.create(
            name=name,
            description=description,
            short_description=short_description,
            version=version,
            developer=developer,
            category=category,
            user_id=session['user_id'],
            uploaded_by=session['username'],
            price=price,
            external_url=external_url
        )

        flash('App submitted successfully', 'success')
        return redirect(url_for('views.publisher_dashboard'))

    return render_template('publisher/add_app.html', categories=Config.CATEGORIES)

@views.route('/publisher/edit_app/<int:app_id>', methods=['GET', 'POST'])
def publisher_edit_app(app_id):
    """Publisher edit app"""
    if not session.get('user_id'):
        flash('Please log in', 'error')
        return redirect(url_for('views.login'))

    app = App.get_by_id(app_id)
    if not app or app['user_id'] != session['user_id']:
        flash('Access denied', 'error')
        return redirect(url_for('views.publisher_dashboard'))

    if request.method == 'POST':
        # Update app
        update_data = {
            'name': sanitize_input(request.form.get('name')),
            'description': sanitize_input(request.form.get('description')),
            'short_description': sanitize_input(request.form.get('short_description')),
            'version': sanitize_input(request.form.get('version')),
            'developer': sanitize_input(request.form.get('developer')),
            'category': sanitize_input(request.form.get('category')),
            'price': float(request.form.get('price', 0)),
            'external_url': sanitize_input(request.form.get('external_url'))
        }

        App.update(app_id, **update_data)
        flash('App updated successfully', 'success')
        return redirect(url_for('views.publisher_dashboard'))

    return render_template('publisher/edit_app.html', app=app, categories=Config.CATEGORIES)

# User management routes (admin only)
@views.route('/admin/add_user', methods=['GET', 'POST'])
def admin_add_user():
    """Add new user"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    if request.method == 'POST':
        username = sanitize_input(request.form.get('username'))
        email = sanitize_input(request.form.get('email'))
        password = request.form.get('password')
        role = sanitize_input(request.form.get('role'))

        # Check if user exists
        if User.get_by_username(username):
            flash('Username already exists', 'error')
            return render_template('admin/add_user.html')

        if User.get_by_email(email):
            flash('Email already exists', 'error')
            return render_template('admin/add_user.html')

        # Create user
        user_id = User.create(username, email, password, role)

        # Log action
        log_admin_action(
            session['user_id'],
            session['username'],
            'add_user',
            f'Added user: {username} (ID: {user_id})'
        )

        flash('User created successfully', 'success')
        return redirect(url_for('views.admin_users'))

    return render_template('admin/add_user.html')

@views.route('/admin/edit_user/<int:user_id>', methods=['GET', 'POST'])
def admin_edit_user(user_id):
    """Edit user"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    user = User.get_by_id(user_id)
    if not user:
        abort(404)

    if request.method == 'POST':
        update_data = {
            'username': sanitize_input(request.form.get('username')),
            'email': sanitize_input(request.form.get('email')),
            'role': sanitize_input(request.form.get('role')),
            'is_active': bool(request.form.get('is_active'))
        }

        User.update(user_id, **update_data)

        # Log action
        log_admin_action(
            session['user_id'],
            session['username'],
            'edit_user',
            f'Edited user: {update_data["username"]} (ID: {user_id})'
        )

        flash('User updated successfully', 'success')
        return redirect(url_for('views.admin_users'))

    return render_template('admin/edit_user.html', user=user)

@views.route('/admin/delete_user/<int:user_id>', methods=['POST'])
def admin_delete_user(user_id):
    """Delete user"""
    if not session.get('user_id') or session.get('role') != 'admin':
        flash('Access denied', 'error')
        return redirect(url_for('views.login'))

    user = User.get_by_id(user_id)
    if not user:
        abort(404)

    # Don't allow deleting yourself
    if user_id == session['user_id']:
        flash('Cannot delete your own account', 'error')
        return redirect(url_for('views.admin_users'))

    username = user['username']
    User.delete(user_id)

    # Log action
    log_admin_action(
        session['user_id'],
        session['username'],
        'delete_user',
        f'Deleted user: {username} (ID: {user_id})'
    )

    flash('User deleted successfully', 'success')
    return redirect(url_for('views.admin_users'))
