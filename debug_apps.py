#!/usr/bin/env python3
"""Debug apps display issue"""

from app.models import App, User, LoginLog, get_db
import sqlite3

def debug_apps():
    """Debug apps display issue"""
    print("🔍 Debugging Apps Display Issue")
    print("=" * 50)
    
    try:
        # Test direct database query
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check apps table
            cursor.execute("SELECT COUNT(*) FROM apps")
            app_count = cursor.fetchone()[0]
            print(f"📊 Total apps in database: {app_count}")
            
            if app_count > 0:
                # Get first few apps directly
                cursor.execute("SELECT id, name, developer, created_at FROM apps LIMIT 5")
                apps_direct = cursor.fetchall()
                print(f"📝 Direct database query results:")
                for app in apps_direct:
                    print(f"  - ID: {app[0]}, Name: {app[1]}, Developer: {app[2]}, Created: {app[3]}")
            
            # Test App.get_all() method
            print(f"\n🧪 Testing App.get_all() method:")
            apps_model = App.get_all()
            print(f"📊 App.get_all() returned: {len(apps_model)} apps")
            
            if apps_model:
                print(f"📝 First app from model:")
                first_app = apps_model[0]
                print(f"  - Type: {type(first_app)}")
                print(f"  - Keys: {list(first_app.keys()) if isinstance(first_app, dict) else 'Not a dict'}")
                if isinstance(first_app, dict):
                    print(f"  - Name: {first_app.get('name', 'No name')}")
                    print(f"  - ID: {first_app.get('id', 'No ID')}")
                    print(f"  - Created: {first_app.get('created_at', 'No created_at')}")
            else:
                print("⚠️  App.get_all() returned empty list!")
            
            # Test with limit
            print(f"\n🧪 Testing App.get_all(limit=5):")
            apps_limited = App.get_all(limit=5)
            print(f"📊 App.get_all(limit=5) returned: {len(apps_limited)} apps")
            
            # Test login logs
            print(f"\n🔍 Testing Login Logs:")
            cursor.execute("SELECT COUNT(*) FROM login_logs")
            login_count = cursor.fetchone()[0]
            print(f"📊 Total login logs in database: {login_count}")
            
            # Create some test login logs
            print(f"\n🔧 Creating test login logs:")
            
            # Successful login
            LoginLog.create(
                user_id=1,
                username='0xmrpepe',
                success=True,
                ip_address='*************',
                user_agent='Mozilla/5.0 Test Browser',
                failure_reason=None
            )
            print("✅ Created successful login log")
            
            # Failed login
            LoginLog.create(
                user_id=None,
                username='hacker123',
                success=False,
                ip_address='*************',
                user_agent='Mozilla/5.0 Malicious Browser',
                failure_reason='Invalid credentials'
            )
            print("✅ Created failed login log")
            
            # Another failed login
            LoginLog.create(
                user_id=None,
                username='admin',
                success=False,
                ip_address='*********',
                user_agent='curl/7.68.0',
                failure_reason='Account locked'
            )
            print("✅ Created another failed login log")
            
            # Test LoginLog.get_all()
            login_logs = LoginLog.get_all(limit=10)
            print(f"📊 LoginLog.get_all() returned: {len(login_logs)} logs")
            
            if login_logs:
                print(f"📝 Login logs:")
                for i, log in enumerate(login_logs[:3]):
                    success_text = "SUCCESS" if log.get('success') else "FAILED"
                    username = log.get('username', 'Unknown')
                    reason = log.get('failure_reason', 'N/A')
                    print(f"  {i+1}. {username} - {success_text} - {reason}")
            
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_apps()
