#!/usr/bin/env python3
"""
Route Testing Script
This script tests all routes and endpoints to ensure they work correctly
"""

import requests
import time

BASE_URL = "http://localhost:5000"

def test_route(url, method='GET', data=None, expected_status=200, description=""):
    """Test a single route"""
    try:
        if method == 'GET':
            response = requests.get(url, timeout=10)
        elif method == 'POST':
            response = requests.post(url, data=data, timeout=10)
        
        if response.status_code == expected_status:
            print(f"✅ {description or url} - Status: {response.status_code}")
            return True
        else:
            print(f"❌ {description or url} - Expected: {expected_status}, Got: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ {description or url} - Error: {str(e)}")
        return False

def test_all_routes():
    """Test all application routes"""
    print("🚀 Testing All Application Routes")
    print("=" * 50)
    
    tests = [
        # Public routes
        (f"{BASE_URL}/", "GET", None, 200, "Home page"),
        (f"{BASE_URL}/gate", "GET", None, 200, "Gate page"),
        (f"{BASE_URL}/login", "GET", None, 200, "Login page"),
        
        # API routes
        (f"{BASE_URL}/api/apps", "GET", None, 200, "API - Get apps"),
        (f"{BASE_URL}/api/stats", "GET", None, 200, "API - Get stats"),
        
        # App detail pages (test with existing app IDs)
        (f"{BASE_URL}/app/1", "GET", None, 200, "App detail page - App 1"),
        (f"{BASE_URL}/app/2", "GET", None, 200, "App detail page - App 2"),
        
        # Download routes (should redirect or serve files)
        (f"{BASE_URL}/download/1", "GET", None, [200, 302, 404], "Download - App 1"),
        
        # Admin routes (should redirect to login for unauthorized)
        (f"{BASE_URL}/admin", "GET", None, [200, 302], "Admin dashboard"),
        (f"{BASE_URL}/admin/apps", "GET", None, [200, 302], "Admin apps"),
        (f"{BASE_URL}/admin/users", "GET", None, [200, 302], "Admin users"),
        
        # Publisher routes (should redirect to login for unauthorized)
        (f"{BASE_URL}/publisher", "GET", None, [200, 302], "Publisher dashboard"),
        
        # Test routes that should return 404
        (f"{BASE_URL}/nonexistent", "GET", None, 404, "Non-existent route"),
        (f"{BASE_URL}/app/999", "GET", None, 404, "Non-existent app"),
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        url, method, data, expected_status, description = test
        
        # Handle multiple expected status codes
        if isinstance(expected_status, list):
            success = False
            for status in expected_status:
                if test_route(url, method, data, status, description):
                    success = True
                    break
            if success:
                passed += 1
        else:
            if test_route(url, method, data, expected_status, description):
                passed += 1
        
        time.sleep(0.1)  # Small delay between requests
    
    print("\n" + "=" * 50)
    print(f"📊 Route Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All routes are working correctly!")
    else:
        print("❌ Some routes have issues. Check the errors above.")
    
    return passed == total

def test_specific_functionality():
    """Test specific functionality"""
    print("\n🧪 Testing Specific Functionality")
    print("=" * 50)
    
    try:
        # Test app listing with parameters
        response = requests.get(f"{BASE_URL}/api/apps?limit=5", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and 'apps' in data:
                print(f"✅ API apps with limit - Retrieved {len(data['apps'])} apps")
            else:
                print("❌ API apps response format incorrect")
        
        # Test search functionality
        response = requests.get(f"{BASE_URL}/?search=pepe", timeout=10)
        if response.status_code == 200:
            print("✅ Search functionality working")
        else:
            print("❌ Search functionality failed")
        
        # Test category filtering
        response = requests.get(f"{BASE_URL}/?category=RAT", timeout=10)
        if response.status_code == 200:
            print("✅ Category filtering working")
        else:
            print("❌ Category filtering failed")
        
        # Test pagination
        response = requests.get(f"{BASE_URL}/?page=1", timeout=10)
        if response.status_code == 200:
            print("✅ Pagination working")
        else:
            print("❌ Pagination failed")
        
        print("🎉 All functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Functionality test error: {str(e)}")
        return False

def main():
    """Main testing function"""
    print("🚀 Starting Comprehensive Route Testing")
    print("=" * 50)
    
    # Wait a moment for server to be ready
    time.sleep(2)
    
    # Test all routes
    routes_ok = test_all_routes()
    
    # Test specific functionality
    functionality_ok = test_specific_functionality()
    
    print("\n" + "=" * 50)
    print("📊 FINAL TEST RESULTS")
    print("=" * 50)
    
    if routes_ok and functionality_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✅ All routes are working correctly")
        print("✅ All functionality is working correctly")
        print("✅ Application is ready for use")
    else:
        print("❌ SOME TESTS FAILED!")
        if not routes_ok:
            print("❌ Route testing failed")
        if not functionality_ok:
            print("❌ Functionality testing failed")
    
    return routes_ok and functionality_ok

if __name__ == '__main__':
    main()
