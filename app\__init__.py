import os
from flask import Flask
from app.config import Config
from app.models import init_database
import logging

# Create Flask app
app = Flask(__name__)
app.config.from_object(Config)

# Initialize configuration
Config.init_app(app)

# Setup logging
log_folder = app.config.get('LOG_FOLDER', 'app/logs')
log_file = app.config.get('LOG_FILE', 'app.log')
log_path = os.path.join(log_folder, log_file)

# Ensure log directory exists
os.makedirs(log_folder, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s: %(message)s',
    handlers=[
        logging.FileHandler(log_path),
        logging.StreamHandler()
    ]
)

# Initialize database
init_database()

# Import and register routes
from app.views.routes import views
from app.api.routes import api
app.register_blueprint(views)
app.register_blueprint(api, url_prefix='/api')
