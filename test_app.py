#!/usr/bin/env python3
"""
Simple test script to verify the Flask application works correctly
"""

import os
import sys
import time

def test_app_startup():
    """Test if the app starts without errors"""
    print("🧪 Testing Flask App Startup...")
    
    try:
        # Import the app
        from app import app
        from app.config import Config
        
        print("✅ App imports successfully")
        
        # Test configuration
        assert app.config['SECRET_KEY'] is not None
        print("✅ Secret key is configured")
        
        # Test database initialization
        from app.models import User, App
        users = User.get_all()
        apps = App.get_all()
        print(f"✅ Database accessible - {len(users)} users, {len(apps)} apps")
        
        # Test routes registration
        rules = [str(rule) for rule in app.url_map.iter_rules()]
        expected_routes = ['/login', '/admin', '/api/apps', '/gate']
        for route in expected_routes:
            assert any(route in rule for rule in rules), f"Route {route} not found"
        print("✅ All expected routes are registered")
        
        print("🎉 All startup tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Startup test failed: {e}")
        return False

def test_basic_functionality():
    """Test basic app functionality"""
    print("\n🧪 Testing Basic Functionality...")
    
    try:
        from app import app
        
        with app.test_client() as client:
            # Test gate page
            response = client.get('/gate')
            assert response.status_code == 200
            print("✅ Gate page loads")
            
            # Test API endpoints
            response = client.get('/api/apps')
            assert response.status_code == 200
            data = response.get_json()
            assert 'success' in data
            print("✅ API apps endpoint works")
            
            response = client.get('/api/stats')
            assert response.status_code == 200
            data = response.get_json()
            assert 'success' in data
            print("✅ API stats endpoint works")
            
            # Test login page
            response = client.get('/login')
            assert response.status_code == 200
            print("✅ Login page loads")
            
        print("🎉 All functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
        return False

def test_file_operations():
    """Test file upload functionality"""
    print("\n🧪 Testing File Operations...")
    
    try:
        from app.views.routes import allowed_file, handle_file_upload
        from app.config import Config
        
        # Test allowed file function
        assert allowed_file('test.zip') == True
        assert allowed_file('test.exe') == True
        assert allowed_file('test.txt') == False
        print("✅ File extension validation works")
        
        # Test directory creation
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(os.path.join(Config.UPLOAD_FOLDER, 'apps'), exist_ok=True)
        os.makedirs(os.path.join(Config.UPLOAD_FOLDER, 'screenshots'), exist_ok=True)
        print("✅ Upload directories created")
        
        print("🎉 All file operation tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ File operation test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Flask App Tests")
    print("=" * 50)
    
    tests = [
        test_app_startup,
        test_basic_functionality,
        test_file_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        time.sleep(0.5)  # Small delay between tests
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The app is ready to run.")
        print("\nTo start the server, run:")
        print("python run.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == '__main__':
    main()
