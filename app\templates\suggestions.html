{% extends "base.html" %}

{% block title %}Submit Suggestions - App Store{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('views.index') }}">Home</a></li>
            <li class="breadcrumb-item active">Submit Suggestions</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h2><i class="bi bi-lightbulb"></i> Submit Your Suggestions</h2>
                    <p class="mb-0 text-muted">Help us improve the platform by sharing your ideas and feedback!</p>
                </div>
                <div class="card-body">
                    <form id="suggestionForm">
                        <div class="mb-3">
                            <label for="category" class="form-label">Category:</label>
                            <select class="form-select" id="category" required>
                                <option value="">Select a category...</option>
                                {% for cat in categories %}
                                <option value="{{ cat }}">{{ cat.replace('_', ' ').title() }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="title" class="form-label">Title:</label>
                            <input type="text" class="form-control" id="title" required
                                   placeholder="Brief title for your suggestion">
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description:</label>
                            <textarea class="form-control" id="description" rows="5" required
                                      placeholder="Detailed description of your suggestion..."></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="priority" class="form-label">Priority:</label>
                            <select class="form-select" id="priority">
                                <option value="low">Low</option>
                                <option value="normal" selected>Normal</option>
                                <option value="high">High</option>
                                <option value="urgent">Urgent</option>
                            </select>
                        </div>
                        
                        <div class="alert alert-info">
                            <i class="bi bi-shield-check"></i>
                            Your suggestion will be encrypted and securely stored. We review all suggestions and may contact you for clarification.
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('views.index') }}" class="btn btn-secondary me-md-2">Cancel</a>
                            <button type="button" class="btn btn-primary" onclick="submitSuggestion()">
                                <i class="bi bi-send"></i> Submit Suggestion
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Guidelines -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Suggestion Guidelines</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-check-circle text-success"></i> Good Suggestions:</h6>
                            <ul class="small">
                                <li>Clear and specific descriptions</li>
                                <li>Constructive feedback</li>
                                <li>Feature requests with use cases</li>
                                <li>UI/UX improvements</li>
                                <li>Performance optimizations</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-x-circle text-danger"></i> Avoid:</h6>
                            <ul class="small">
                                <li>Vague or unclear requests</li>
                                <li>Duplicate suggestions</li>
                                <li>Personal attacks or complaints</li>
                                <li>Requests for illegal content</li>
                                <li>Spam or irrelevant content</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Categories Explanation -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Category Descriptions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <dl class="small">
                                <dt>Feature Request</dt>
                                <dd>New functionality or features you'd like to see</dd>
                                
                                <dt>Bug Report</dt>
                                <dd>Issues or problems you've encountered</dd>
                                
                                <dt>UI Improvement</dt>
                                <dd>User interface and design suggestions</dd>
                                
                                <dt>Performance</dt>
                                <dd>Speed, loading, or optimization suggestions</dd>
                            </dl>
                        </div>
                        <div class="col-md-6">
                            <dl class="small">
                                <dt>Security</dt>
                                <dd>Security-related improvements or concerns</dd>
                                
                                <dt>Content Request</dt>
                                <dd>Requests for specific apps or content</dd>
                                
                                <dt>Other</dt>
                                <dd>General feedback or suggestions not covered above</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function submitSuggestion() {
    const category = document.getElementById('category').value;
    const title = document.getElementById('title').value;
    const description = document.getElementById('description').value;
    const priority = document.getElementById('priority').value;
    
    if (!category || !title || !description) {
        alert('Please fill in all required fields.');
        return;
    }
    
    const data = {
        category: category,
        title: title,
        description: description,
        priority: priority
    };
    
    // Disable submit button
    const submitBtn = event.target;
    submitBtn.disabled = true;
    submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Submitting...';
    
    fetch('/suggestions', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            document.getElementById('suggestionForm').reset();
            window.location.href = '{{ url_for("views.index") }}';
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while submitting the suggestion.');
    })
    .finally(() => {
        // Re-enable submit button
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="bi bi-send"></i> Submit Suggestion';
    });
}
</script>
{% endblock %}
