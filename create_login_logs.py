#!/usr/bin/env python3
"""Create test login logs"""

from app.models import LoginLog
import random

def create_test_login_logs():
    """Create test login logs"""
    print("🔧 Creating Test Login Logs")
    print("=" * 40)
    
    # Successful logins
    successful_logins = [
        ('0xmrpepe', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
        ('publisher1', '192.168.1.101', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'),
        ('testuser', '192.168.1.102', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'),
        ('0xmrpepe', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
        ('admin', '********', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
    ]
    
    # Failed logins
    failed_logins = [
        ('hacker123', '***********', 'curl/7.68.0', 'Invalid credentials'),
        ('admin', '***********', 'python-requests/2.25.1', 'Account locked'),
        ('root', '***********', 'Mozilla/5.0 (compatible; Nmap Scripting Engine)', 'User not found'),
        ('administrator', '***********', 'sqlmap/1.5.2', 'Too many failed attempts'),
        ('guest', '***********', 'Wget/1.20.3', 'Invalid credentials'),
        ('test', '***********', 'curl/7.68.0', 'Account disabled'),
        ('demo', '***********', 'python-requests/2.25.1', 'Invalid credentials'),
        ('user', '***********', 'Mozilla/5.0 (compatible; Nmap Scripting Engine)', 'Brute force detected'),
    ]
    
    # Create successful logins
    for i, (username, ip, user_agent) in enumerate(successful_logins):
        try:
            log_id = LoginLog.create(
                user_id=1 if username == '0xmrpepe' else random.randint(2, 5),
                username=username,
                success=True,
                ip_address=ip,
                user_agent=user_agent,
                failure_reason=None
            )
            print(f"✅ Created successful login #{i+1}: {username} from {ip}")
        except Exception as e:
            print(f"❌ Failed to create successful login #{i+1}: {e}")
    
    # Create failed logins
    for i, (username, ip, user_agent, reason) in enumerate(failed_logins):
        try:
            log_id = LoginLog.create(
                user_id=None,
                username=username,
                success=False,
                ip_address=ip,
                user_agent=user_agent,
                failure_reason=reason
            )
            print(f"✅ Created failed login #{i+1}: {username} from {ip} - {reason}")
        except Exception as e:
            print(f"❌ Failed to create failed login #{i+1}: {e}")
    
    # Verify logs were created
    try:
        all_logs = LoginLog.get_all(limit=20)
        successful_count = len([log for log in all_logs if log.get('success')])
        failed_count = len([log for log in all_logs if not log.get('success')])
        
        print(f"\n📊 Login Log Summary:")
        print(f"✅ Successful logins: {successful_count}")
        print(f"❌ Failed logins: {failed_count}")
        print(f"📝 Total login logs: {len(all_logs)}")
        
        if all_logs:
            print(f"\n📝 Recent login attempts:")
            for i, log in enumerate(all_logs[:5]):
                status = "SUCCESS" if log.get('success') else "FAILED"
                username = log.get('username', 'Unknown')
                ip = log.get('ip_address', 'Unknown IP')
                reason = log.get('failure_reason', 'N/A')
                print(f"  {i+1}. {username} from {ip} - {status} - {reason}")
                
    except Exception as e:
        print(f"❌ Failed to retrieve login logs: {e}")

if __name__ == "__main__":
    create_test_login_logs()
    print(f"\n🎉 Login logs created! Check at: http://localhost:5000/admin/logs?type=login")
