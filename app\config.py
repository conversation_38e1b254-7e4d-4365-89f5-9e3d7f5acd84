import os
import secrets
from datetime import timedelta

class Config:
    # Generate a secure secret key if not provided
    SECRET_KEY = "00c106af5387312d935be97842e1f0001d21c6684c22d010bb46536b438adb61b05e8bb5d37723bdb0d19e4a060f08b77052cc9f3d067afdf2b12d1dba015e4bfe8dd0db0bc19cc0f7953a730632076d1dbfb85e05b4df6f2ba0e62364ba211807856c41cbd02d8619bd790ca1c268a2f645bd76bba38f950f22558c98661b5bd01820af2704ce580e213e1f9e161a41eba20546a414ce52fb5168d80daceeb02c5671c39384160495aaced94d7b2daea245a630de06c5b226d0c280618fab6512fabadea7967d1fcf0bb237fff17d9e4403c4c2beced5b8d3a58fd6ede5ab8b0f9eebc21d846a2923b42d77756f25ed64aedcf8c61a621f3d22af457b7ce6c0236673c2b07d8f65a1bffa8cd3f37e97d891311d605f5dfad628d8a91e0ef1d73fed9b9ccfa3f14beb9dbbd628c565c6e8132dee7143f55d82d2e6af112501298b5cf634298dd5ff0de9c8e6ab321980c24ac7f7c88920456232d2bb6a969b682760561731398275be073aa857e3669f3ee9d2c5e1493dc62baf1fed6ab51ecd8fbd9923f1873cef51cd6c0b4429b2d3d0772263737c7cc56116538a83d4ee22b873a930c3468279d67329c6f8f30287abe0f9abf6d2d03a69ea180c33f102853f1ce155c59bb4bff331ebc5784a5947748756e291b1c5fa00ac225fe56bb3e2a1a37c63dacafb686889bc8a48fc46122a6c90d89852a1758a628e8c08c7d59da127738e840d959e986c79ea051e7244c4649fc7b9c19e8f6fdba75e782122c4d7e68983c9db6603196eb5e6725dbc0e222ebd4c02fb2cea77cac975a91ed54a12bd5b5e079b6a3f0874599c340cdd415c81e8c81690043964c922d943ba49600d7c5fe0f5ca675fed7d9fb68cb6dbbafe260f46a31d5d45e816556926b08793ea34d9fa4335819c12db543c3e70e141572ccfa25aa013953cff727b0c2ad35fe84ebd80cb27ba2a1f05493db3ea4cb2c44231a8e0bb2c7a15dde053d681414998aa4f3df4cbb9b28df056b2a96c8ca8364d4e2ae13b77669e0c0a9c1fa028b67e39ecdbbc2cb56a020525fb740297a1632474211aa1d224efe0e44c99950a194674942a60d550852674d4d28003e6ccccab4cb3db2d15270f845ed94daaae9a77e411553a1f1aa950961f26305f211486e38f71bc1368b3c6675446e69d8810e1cf0d593e82b6d5f2ffda468c4e039f3de4bb33d9d9bac1f4cec6950a6c69c94f8e098ee7300e08b3e9078d02699e318e77550db2ce5782c8e429834de1635b96428e30cb9188a7d3e883ef6b27a1491b6436cf3fd7258d1c0db79d88cfc17be5b2b8168d75b3d9d5c7729d4078d2744e319d761858fd20ad16335cf878774b7ee56c85a0955fc0d29ab26bc22c63"
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///instance/app_store.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    CATEGORIES = [
    'RAT', 'Crypter', 'Stealer', 'Software',
    'Others'
    ]

    # Upload settings
    UPLOAD_FOLDER = 'uploads'
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'zip', 'exe', 'rar', '7z'}

    # Logging settings
    LOG_FOLDER = 'logs'
    LOG_FILE = 'app_store.log'

    # Admin settings
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME', '0xmrpepe')
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD', 'admin123')  # Default password for development
    if ADMIN_PASSWORD == 'admin123':
        import warnings
        warnings.warn('Using default admin password! Please set ADMIN_PASSWORD environment variable for better security.')

    # Enhanced Session Security Settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)  # Reduced from 24 hours
    SESSION_COOKIE_SECURE = os.environ.get('FLASK_ENV') == 'production'  # HTTPS only in production
    SESSION_COOKIE_HTTPONLY = True  # Prevent XSS access to cookies
    SESSION_COOKIE_SAMESITE = 'Lax'  # CSRF protection
    SESSION_COOKIE_NAME = 'pepe_session'  # Custom session name

    # Security Headers
    SECURITY_HEADERS = {
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' cdn.jsdelivr.net; img-src 'self' data:; font-src 'self' cdn.jsdelivr.net;"
    }

    # Rate limiting settings
    RATELIMIT_STORAGE_URL = 'memory://'
    RATELIMIT_DEFAULT = "100 per hour"

    # Login attempt settings
    MAX_LOGIN_ATTEMPTS = 5
    LOGIN_ATTEMPT_TIMEOUT = timedelta(minutes=15)

    @staticmethod
    def init_app(app):
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(os.path.join(Config.UPLOAD_FOLDER, 'apps'), exist_ok=True)
        os.makedirs(os.path.join(Config.UPLOAD_FOLDER, 'screenshots'), exist_ok=True)
        os.makedirs(Config.LOG_FOLDER, exist_ok=True)
