{% extends "base.html" %}

{% block title %}My Posts - Publisher Dashboard{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('views.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('views.publisher_dashboard') }}">Publisher</a></li>
            <li class="breadcrumb-item active">Posts</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-12">
            <!-- Posts Management -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3><i class="bi bi-file-text"></i> My Posts</h3>
                    <a href="{{ url_for('views.publisher_add_post') }}" class="btn btn-primary">
                        <i class="bi bi-plus"></i> New Post
                    </a>
                </div>
                <div class="card-body">
                    {% if posts %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Views</th>
                                    <th>Created</th>
                                    <th>Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for post in posts %}
                                <tr>
                                    <td>
                                        <strong>{{ post.title }}</strong>
                                        {% if post.excerpt %}
                                        <br><small class="text-muted">{{ post.excerpt[:100] }}{% if post.excerpt|length > 100 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ post.category.replace('_', ' ').title() }}</span>
                                    </td>
                                    <td>
                                        {% if post.status == 'published' %}
                                        <span class="badge bg-success">Published</span>
                                        {% elif post.status == 'draft' %}
                                        <span class="badge bg-warning">Draft</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ post.status.title() }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ post.view_count or 0 }}</span>
                                    </td>
                                    <td>
                                        <small>{{ post.created_at | datetime('%m/%d/%Y') }}</small>
                                    </td>
                                    <td>
                                        <small>{{ post.updated_at | datetime('%m/%d/%Y') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            {% if post.status == 'published' %}
                                            <a href="/post/{{ post.id }}" class="btn btn-outline-primary" target="_blank">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            {% endif %}
                                            <a href="{{ url_for('views.publisher_edit_post', post_id=post.id) }}" class="btn btn-outline-secondary">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" onclick="deletePost({{ post.id }})">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="bi bi-file-text display-1 text-muted"></i>
                        <h4 class="text-muted">No Posts Yet</h4>
                        <p class="text-muted">Create your first post to share content with your audience!</p>
                        <a href="{{ url_for('views.publisher_add_post') }}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> Create First Post
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-primary">{{ posts|length }}</h3>
                    <small class="text-muted">Total Posts</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-success">{{ posts|selectattr('status', 'equalto', 'published')|list|length }}</h3>
                    <small class="text-muted">Published</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-warning">{{ posts|selectattr('status', 'equalto', 'draft')|list|length }}</h3>
                    <small class="text-muted">Drafts</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card text-center">
                <div class="card-body">
                    <h3 class="text-info">{{ posts|sum(attribute='view_count') or 0 }}</h3>
                    <small class="text-muted">Total Views</small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function deletePost(postId) {
    if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
        fetch(`/publisher/posts/delete/${postId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the post.');
        });
    }
}
</script>
{% endblock %}
