#!/usr/bin/env python3
"""
Database Reset and Enhancement Script
This script will:
1. Reset the entire database
2. Create enhanced tables with better structure
3. Add comprehensive test data
4. Verify data integrity
"""

import os
import sqlite3
import random
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash

# Database path
DATABASE_PATH = os.path.join('app', 'database.db')

def reset_database():
    """Reset the entire database"""
    print("🗑️  Resetting database...")
    
    # Remove existing database
    if os.path.exists(DATABASE_PATH):
        os.remove(DATABASE_PATH)
        print("✅ Old database removed")
    
    # Ensure app directory exists
    os.makedirs('app', exist_ok=True)
    
    # Create new database
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()
    
    print("✅ New database created")
    return conn, cursor

def create_enhanced_tables(cursor):
    """Create enhanced database tables"""
    print("📋 Creating enhanced tables...")
    
    # Users table with enhanced fields
    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'publisher',
            is_active BOOLEAN DEFAULT 1,
            first_name TEXT,
            last_name TEXT,
            bio TEXT,
            avatar_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_login TIMESTAMP,
            login_count INTEGER DEFAULT 0,
            email_verified BOOLEAN DEFAULT 0
        )
    ''')
    
    # Apps table with enhanced fields
    cursor.execute('''
        CREATE TABLE apps (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            description TEXT NOT NULL,
            short_description TEXT NOT NULL,
            version TEXT NOT NULL,
            developer TEXT NOT NULL,
            uploaded_by TEXT NOT NULL DEFAULT 'Admin',
            category TEXT NOT NULL,
            price REAL DEFAULT 0.0,
            rating REAL DEFAULT 0.0,
            downloads INTEGER DEFAULT 0,
            file_path TEXT,
            external_url TEXT,
            file_size INTEGER DEFAULT 0,
            icon_path TEXT,
            user_id INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            is_featured BOOLEAN DEFAULT 0,
            is_approved BOOLEAN DEFAULT 1,
            tags TEXT,
            changelog TEXT,
            system_requirements TEXT,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Screenshots table
    cursor.execute('''
        CREATE TABLE screenshots (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            app_id INTEGER NOT NULL,
            file_path TEXT NOT NULL,
            caption TEXT,
            order_num INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (app_id) REFERENCES apps (id) ON DELETE CASCADE
        )
    ''')
    
    # Download logs table
    cursor.execute('''
        CREATE TABLE download_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            app_id INTEGER NOT NULL,
            ip_address TEXT NOT NULL,
            user_agent TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            download_type TEXT DEFAULT 'direct',
            FOREIGN KEY (app_id) REFERENCES apps (id)
        )
    ''')
    
    # Admin logs table
    cursor.execute('''
        CREATE TABLE admin_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            username TEXT NOT NULL,
            action TEXT NOT NULL,
            details TEXT,
            ip_address TEXT NOT NULL,
            user_agent TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Login logs table
    cursor.execute('''
        CREATE TABLE login_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            username TEXT NOT NULL,
            success BOOLEAN NOT NULL,
            ip_address TEXT NOT NULL,
            user_agent TEXT,
            failure_reason TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # App ratings table with enhanced fields
    cursor.execute('''
        CREATE TABLE app_ratings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            app_id INTEGER NOT NULL,
            user_id INTEGER,
            rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
            review TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            ip_address TEXT,
            fingerprint_id TEXT,
            is_verified BOOLEAN DEFAULT 0,
            helpful_count INTEGER DEFAULT 0,
            FOREIGN KEY (app_id) REFERENCES apps (id),
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Login attempts table
    cursor.execute('''
        CREATE TABLE login_attempts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            ip_address TEXT NOT NULL,
            username TEXT,
            success BOOLEAN NOT NULL,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            user_agent TEXT
        )
    ''')
    
    # Session tokens table
    cursor.execute('''
        CREATE TABLE session_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token_hash TEXT NOT NULL UNIQUE,
            ip_address TEXT NOT NULL,
            user_agent TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Fingerprints table
    cursor.execute('''
        CREATE TABLE fingerprints (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            uuid TEXT UNIQUE NOT NULL,
            fingerprint TEXT UNIQUE NOT NULL,
            hmac_key TEXT NOT NULL,
            browser_data TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            last_seen TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            visit_count INTEGER DEFAULT 1
        )
    ''')
    
    print("✅ Enhanced tables created")

def add_test_users(cursor):
    """Add comprehensive test users"""
    print("👥 Adding test users...")
    
    users = [
        {
            'username': '0xmrpepe',
            'email': '<EMAIL>',
            'password': 'admin123',
            'role': 'admin',
            'first_name': 'Mr',
            'last_name': 'Pepe',
            'bio': 'The legendary admin of PEPE Store. Master of all things digital and memes.'
        },
        {
            'username': 'hackerman',
            'email': '<EMAIL>',
            'password': 'hacker123',
            'role': 'publisher',
            'first_name': 'Elite',
            'last_name': 'Hacker',
            'bio': 'Professional software developer and security researcher.'
        },
        {
            'username': 'cryptodev',
            'email': '<EMAIL>',
            'password': 'crypto123',
            'role': 'publisher',
            'first_name': 'Crypto',
            'last_name': 'Developer',
            'bio': 'Blockchain enthusiast and cryptocurrency tool developer.'
        },
        {
            'username': 'malwareanalyst',
            'email': '<EMAIL>',
            'password': 'analyst123',
            'role': 'publisher',
            'first_name': 'Malware',
            'last_name': 'Analyst',
            'bio': 'Security researcher specializing in malware analysis and reverse engineering.'
        },
        {
            'username': 'scriptkiddie',
            'email': '<EMAIL>',
            'password': 'script123',
            'role': 'publisher',
            'first_name': 'Script',
            'last_name': 'Kiddie',
            'bio': 'Aspiring hacker learning the ropes of cybersecurity.'
        }
    ]
    
    for user in users:
        password_hash = generate_password_hash(user['password'])
        cursor.execute('''
            INSERT INTO users (username, email, password_hash, role, is_active, 
                             first_name, last_name, bio, email_verified, login_count)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            user['username'], user['email'], password_hash, user['role'], 1,
            user['first_name'], user['last_name'], user['bio'], 1, random.randint(5, 50)
        ))
    
    print(f"✅ Added {len(users)} test users")

def main():
    """Main function to reset and populate database"""
    print("🚀 Starting Database Reset and Enhancement")
    print("=" * 50)
    
    try:
        # Reset database
        conn, cursor = reset_database()
        
        # Create enhanced tables
        create_enhanced_tables(cursor)
        
        # Add test users
        add_test_users(cursor)
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print("=" * 50)
        print("🎉 Database reset and enhancement completed successfully!")
        print("\n📊 Summary:")
        print("- Enhanced database schema created")
        print("- 5 test users added")
        print("- Ready for test data population")
        print("\n🔑 Admin Credentials:")
        print("Username: 0xmrpepe")
        print("Password: admin123")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == '__main__':
    main()
