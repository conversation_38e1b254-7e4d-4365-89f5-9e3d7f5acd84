#!/usr/bin/env python3
"""Test database connectivity and table structure"""

import sqlite3
import os
from app.models import get_db, Ad<PERSON><PERSON><PERSON>, <PERSON><PERSON>, User

def test_database():
    """Test database connectivity and structure"""
    print("🔍 Testing Database Connectivity")
    print("=" * 50)
    
    # Check if database file exists
    db_path = 'app/database.db'
    if os.path.exists(db_path):
        print(f"✅ Database file exists: {db_path}")
        print(f"📊 File size: {os.path.getsize(db_path)} bytes")
    else:
        print(f"❌ Database file not found: {db_path}")
        return
    
    try:
        # Test direct connection
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"\n📋 Tables in database ({len(tables)}):")
        for table in tables:
            print(f"  - {table[0]}")
        
        # Check admin_logs table specifically
        if ('admin_logs',) in tables:
            print(f"\n🔍 admin_logs table structure:")
            cursor.execute("PRAGMA table_info(admin_logs)")
            columns = cursor.fetchall()
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # Count records
            cursor.execute("SELECT COUNT(*) FROM admin_logs")
            count = cursor.fetchone()[0]
            print(f"\n📊 admin_logs records: {count}")
            
            if count > 0:
                cursor.execute("SELECT * FROM admin_logs LIMIT 3")
                records = cursor.fetchall()
                print(f"📝 Sample records:")
                for record in records:
                    print(f"  {record}")
        else:
            print(f"\n❌ admin_logs table not found!")
        
        conn.close()
        
        # Test model methods
        print(f"\n🧪 Testing Model Methods:")
        
        # Test AdminLog
        try:
            logs = AdminLog.get_all(limit=5)
            print(f"✅ AdminLog.get_all() returned {len(logs)} logs")
            if logs:
                print(f"📝 First log: {logs[0]}")
        except Exception as e:
            print(f"❌ AdminLog.get_all() failed: {e}")
        
        # Test App
        try:
            apps = App.get_all(limit=5)
            print(f"✅ App.get_all() returned {len(apps)} apps")
            if apps:
                print(f"📝 First app: {apps[0]['name'] if apps[0] else 'None'}")
        except Exception as e:
            print(f"❌ App.get_all() failed: {e}")
        
        # Test User
        try:
            users = User.get_all()
            print(f"✅ User.get_all() returned {len(users)} users")
            if users:
                print(f"📝 First user: {users[0]['username'] if users[0] else 'None'}")
        except Exception as e:
            print(f"❌ User.get_all() failed: {e}")
        
        # Test creating an admin log
        try:
            log_id = AdminLog.create(
                user_id=1,
                username='test_admin',
                action='test_action',
                details='Test log entry from test script',
                ip_address='127.0.0.1',
                user_agent='Test Agent'
            )
            print(f"✅ Created test admin log with ID: {log_id}")
        except Exception as e:
            print(f"❌ Failed to create admin log: {e}")
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")

if __name__ == "__main__":
    test_database()
