#!/usr/bin/env python3
"""
App Store - Run Script
This script starts the Flask application with proper configuration.
"""

import os
import sys
from app import app
from app.config import Config

def main():
    """Main function to run the Flask application"""
    
    if not os.environ.get('SECRET_KEY'):
        os.environ['SECRET_KEY'] = Config.SECRET_KEY
    
    # Create necessary directories
    # directories = [
    #     'uploads',
    #     'uploads/apps',
    #     'uploads/screenshots', 
    #     'logs',
    #     'static',
    #     'static/css',
    #     'static/js'
    # ]
    
    # for directory in directories:
    #     os.makedirs(directory, exist_ok=True)
    #     print(f"✓ Directory '{directory}' ready")
    
    # Database will be auto-initialized by the app/__init__.py
    print("✓ Database initialization handled by app")

    # Check if running in development or production
    debug_mode = os.environ.get('FLASK_ENV') == 'development' or '--debug' in sys.argv
    
    print("\n" + "="*50)
    print("🏪 APP STORE - Starting Server")
    print("="*50)
    print(f"Debug Mode: {'ON' if debug_mode else 'OFF'}")
    print(f"Host: 0.0.0.0")
    print(f"Port: 5000")
    print(f"URL: http://localhost:5000")
    print("\nAdmin Credentials:")
    print(f"Username: {app.config['ADMIN_USERNAME']}")
    print(f"Password: {app.config['ADMIN_PASSWORD']}")
    print("="*50)
    print("Press Ctrl+C to stop the server")
    print("="*50 + "\n")
    
    try:
        # Run the Flask application
        app.run(
            debug=debug_mode,
            host='0.0.0.0',
            port=5000,
            threaded=True
        )
    except KeyboardInterrupt:
        print("\n\n" + "="*50)
        print("🛑 Server stopped by user")
        print("="*50)
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
