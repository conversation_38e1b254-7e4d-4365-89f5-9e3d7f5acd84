#!/usr/bin/env python3
"""
Test Data Population Script
This script adds comprehensive test data to the database including:
- Apps with various categories
- Ratings and reviews
- Download logs
- Admin logs
- Screenshots
"""

import os
import sqlite3
import random
from datetime import datetime, timedelta

# Database path
DATABASE_PATH = os.path.join('app', 'database.db')

def get_db_connection():
    """Get database connection"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn

def add_test_apps():
    """Add comprehensive test apps"""
    print("📱 Adding test apps...")
    
    apps_data = [
        {
            'name': 'PepeRAT Pro',
            'short_description': 'Advanced remote administration tool with stealth capabilities',
            'description': '''# PepeRAT Pro - The Ultimate RAT

**Features:**
- Undetectable by most antivirus software
- Real-time screen monitoring
- Keylogger with advanced filtering
- File transfer capabilities
- Remote shell access
- Cryptocurrency wallet stealer
- Browser password extraction

**System Requirements:**
- Windows 7/8/10/11
- .NET Framework 4.5+
- 50MB free space

**Changelog v2.1:**
- Added new stealth techniques
- Improved stability
- Fixed minor bugs''',
            'version': '2.1.0',
            'developer': 'PepeTeam',
            'category': 'RAT',
            'price': 49.99,
            'user_id': 2,
            'is_featured': 1,
            'tags': 'remote,administration,stealth,advanced',
            'file_size': 2048576,  # 2MB
            'external_url': 'https://example.com/peperat-pro.zip'
        },
        {
            'name': 'CryptoMiner Elite',
            'short_description': 'High-performance cryptocurrency mining software',
            'description': '''# CryptoMiner Elite

Professional-grade mining software supporting multiple cryptocurrencies.

**Supported Coins:**
- Bitcoin (BTC)
- Ethereum (ETH)
- Monero (XMR)
- Litecoin (LTC)

**Features:**
- GPU and CPU mining
- Pool mining support
- Real-time statistics
- Auto-switching algorithms''',
            'version': '1.5.2',
            'developer': 'CryptoDevs',
            'category': 'Software',
            'price': 0.0,
            'user_id': 3,
            'is_featured': 1,
            'tags': 'cryptocurrency,mining,bitcoin,ethereum',
            'file_size': 15728640,  # 15MB
        },
        {
            'name': 'StealthCrypter',
            'short_description': 'Advanced file encryption and obfuscation tool',
            'description': '''# StealthCrypter

Military-grade encryption and obfuscation for your sensitive files.

**Features:**
- AES-256 encryption
- Multiple obfuscation layers
- Anti-debugging techniques
- Custom packers
- FUD (Fully Undetectable) guarantee''',
            'version': '3.0.1',
            'developer': 'StealthTeam',
            'category': 'Crypter',
            'price': 29.99,
            'user_id': 2,
            'is_featured': 0,
            'tags': 'encryption,obfuscation,stealth,security',
            'file_size': 5242880,  # 5MB
        },
        {
            'name': 'InfoStealer Supreme',
            'short_description': 'Comprehensive information gathering tool',
            'description': '''# InfoStealer Supreme

The most advanced information gathering tool available.

**Capabilities:**
- Browser data extraction
- Saved passwords recovery
- Cryptocurrency wallet detection
- System information gathering
- Network reconnaissance
- Social media account discovery''',
            'version': '4.2.0',
            'developer': 'InfoTeam',
            'category': 'Stealer',
            'price': 39.99,
            'user_id': 4,
            'is_featured': 1,
            'tags': 'information,gathering,passwords,wallets',
            'file_size': 8388608,  # 8MB
        },
        {
            'name': 'Network Scanner Pro',
            'short_description': 'Professional network scanning and analysis tool',
            'description': '''# Network Scanner Pro

Professional network security assessment tool.

**Features:**
- Port scanning
- Vulnerability detection
- Network mapping
- Service enumeration
- Report generation''',
            'version': '2.3.1',
            'developer': 'NetSec',
            'category': 'Software',
            'price': 0.0,
            'user_id': 5,
            'is_featured': 0,
            'tags': 'network,scanning,security,analysis',
            'file_size': ********,  # 12MB
        },
        {
            'name': 'PepeBot Builder',
            'short_description': 'Easy-to-use botnet creation and management tool',
            'description': '''# PepeBot Builder

Create and manage your own botnet with ease.

**Features:**
- Drag-and-drop bot builder
- Command & control panel
- DDoS capabilities
- Cryptocurrency mining
- Data exfiltration
- Real-time bot monitoring''',
            'version': '1.8.5',
            'developer': 'PepeTeam',
            'category': 'RAT',
            'price': 99.99,
            'user_id': 2,
            'is_featured': 1,
            'tags': 'botnet,builder,ddos,management',
            'file_size': 25165824,  # 24MB
        },
        {
            'name': 'Social Engineer Toolkit',
            'short_description': 'Comprehensive social engineering framework',
            'description': '''# Social Engineer Toolkit

Complete framework for social engineering attacks.

**Modules:**
- Phishing campaigns
- Email spoofing
- Website cloning
- Credential harvesting
- SMS phishing
- Voice phishing''',
            'version': '5.1.0',
            'developer': 'SocialHackers',
            'category': 'Others',
            'price': 19.99,
            'user_id': 4,
            'is_featured': 0,
            'tags': 'social,engineering,phishing,framework',
            'file_size': 18874368,  # 18MB
        },
        {
            'name': 'Advanced Keylogger',
            'short_description': 'Invisible keylogger with advanced features',
            'description': '''# Advanced Keylogger

Completely invisible keylogger with advanced monitoring capabilities.

**Features:**
- Keystroke logging
- Screenshot capture
- Clipboard monitoring
- Application tracking
- Email reporting
- Stealth mode''',
            'version': '2.7.3',
            'developer': 'StealthMonitor',
            'category': 'Stealer',
            'price': 24.99,
            'user_id': 3,
            'is_featured': 0,
            'tags': 'keylogger,monitoring,stealth,invisible',
            'file_size': 3145728,  # 3MB
        }
    ]
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    app_ids = []
    for app_data in apps_data:
        cursor.execute('''
            INSERT INTO apps (name, description, short_description, version, developer,
                            category, user_id, uploaded_by, price, file_size, external_url,
                            is_featured, tags, system_requirements, changelog)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            app_data['name'], app_data['description'], app_data['short_description'],
            app_data['version'], app_data['developer'], app_data['category'],
            app_data['user_id'], app_data['developer'], app_data['price'],
            app_data['file_size'], app_data.get('external_url'),
            app_data['is_featured'], app_data['tags'],
            'Windows 7/8/10/11, .NET Framework 4.5+',
            'Latest version with bug fixes and improvements'
        ))
        app_ids.append(cursor.lastrowid)
    
    conn.commit()
    conn.close()
    
    print(f"✅ Added {len(apps_data)} test apps")
    return app_ids

def add_test_ratings(app_ids):
    """Add test ratings and reviews"""
    print("⭐ Adding test ratings...")
    
    reviews = [
        "Excellent tool! Works perfectly as described.",
        "Great software, very reliable and easy to use.",
        "Amazing features, definitely worth the price.",
        "Good tool but could use some improvements.",
        "Decent software, does what it says.",
        "Not bad, but I've seen better alternatives.",
        "Okay tool, nothing special but functional.",
        "Disappointing, doesn't work as advertised.",
        "Poor quality, many bugs and issues.",
        "Terrible software, complete waste of money."
    ]
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    rating_count = 0
    for app_id in app_ids:
        # Add 5-15 ratings per app
        num_ratings = random.randint(5, 15)
        for _ in range(num_ratings):
            rating = random.randint(1, 5)
            review = random.choice(reviews) if random.random() > 0.3 else None
            user_id = random.randint(2, 5) if random.random() > 0.5 else None
            
            # Create timestamp within last 30 days
            days_ago = random.randint(0, 30)
            timestamp = datetime.now() - timedelta(days=days_ago)
            
            cursor.execute('''
                INSERT INTO app_ratings (app_id, user_id, rating, review, timestamp,
                                       ip_address, is_verified, helpful_count)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                app_id, user_id, rating, review, timestamp,
                f"192.168.1.{random.randint(1, 254)}", 
                random.choice([0, 1]), random.randint(0, 10)
            ))
            rating_count += 1
    
    conn.commit()
    conn.close()
    
    print(f"✅ Added {rating_count} test ratings")

def add_test_downloads(app_ids):
    """Add test download logs"""
    print("📥 Adding test downloads...")
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    download_count = 0
    for app_id in app_ids:
        # Add 10-50 downloads per app
        num_downloads = random.randint(10, 50)
        for _ in range(num_downloads):
            # Create timestamp within last 60 days
            days_ago = random.randint(0, 60)
            timestamp = datetime.now() - timedelta(days=days_ago)
            
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Mozilla/5.0 (Windows NT 6.1; Win64; x64) AppleWebKit/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
            ]
            
            cursor.execute('''
                INSERT INTO download_logs (app_id, ip_address, user_agent, timestamp, download_type)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                app_id, f"192.168.1.{random.randint(1, 254)}",
                random.choice(user_agents), timestamp,
                random.choice(['direct', 'external'])
            ))
            download_count += 1
        
        # Update app download count
        cursor.execute('UPDATE apps SET downloads = ? WHERE id = ?', (num_downloads, app_id))
    
    conn.commit()
    conn.close()
    
    print(f"✅ Added {download_count} test downloads")

def main():
    """Main function to populate test data"""
    print("🚀 Starting Test Data Population")
    print("=" * 50)
    
    try:
        # Add test apps
        app_ids = add_test_apps()
        
        # Add test ratings
        add_test_ratings(app_ids)
        
        # Add test downloads
        add_test_downloads(app_ids)
        
        print("=" * 50)
        print("🎉 Test data population completed successfully!")
        print(f"\n📊 Summary:")
        print(f"- {len(app_ids)} apps added")
        print(f"- Multiple ratings and reviews added")
        print(f"- Download logs generated")
        print(f"- Database ready for testing")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == '__main__':
    main()
