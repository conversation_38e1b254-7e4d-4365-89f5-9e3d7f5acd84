#!/usr/bin/env python3
"""Add test admin logs and verify functionality"""

from app.models import AdminLog, get_db
from datetime import datetime, timedelta
import random

def add_test_logs():
    """Add test admin logs"""
    print("🔧 Adding Test Admin Logs")
    print("=" * 40)
    
    # Test actions
    actions = [
        ('add_app', 'Added new app: Test App 1'),
        ('edit_app', 'Modified app: Test App 2'),
        ('delete_app', 'Deleted app: Old App'),
        ('add_user', 'Created new user: testuser'),
        ('edit_user', 'Modified user permissions'),
        ('login', 'Admin login successful'),
        ('logout', 'Admin logout'),
        ('view_logs', 'Accessed admin logs'),
        ('backup_db', 'Database backup created'),
        ('system_check', 'System health check performed')
    ]
    
    # Add logs with different timestamps
    for i in range(15):
        action, details = random.choice(actions)
        
        # Create logs with timestamps spread over the last 7 days
        days_ago = random.randint(0, 7)
        hours_ago = random.randint(0, 23)
        minutes_ago = random.randint(0, 59)
        
        try:
            log_id = AdminLog.create(
                user_id=1,
                username='0xmrpepe',
                action=action,
                details=f"{details} (Test #{i+1})",
                ip_address=f"192.168.1.{random.randint(100, 200)}",
                user_agent=f"Mozilla/5.0 Test Agent {i+1}"
            )
            print(f"✅ Created log #{i+1}: {action} (ID: {log_id})")
        except Exception as e:
            print(f"❌ Failed to create log #{i+1}: {e}")
    
    # Verify logs were created
    try:
        logs = AdminLog.get_all(limit=20)
        print(f"\n📊 Total admin logs in database: {len(logs)}")
        
        if logs:
            print(f"📝 Recent logs:")
            for i, log in enumerate(logs[:5]):
                print(f"  {i+1}. {log.get('action', 'Unknown')} - {log.get('details', 'No details')}")
        else:
            print("⚠️  No logs found after creation!")
            
    except Exception as e:
        print(f"❌ Failed to retrieve logs: {e}")

def test_database_tables():
    """Test database table structure"""
    print(f"\n🔍 Testing Database Tables")
    print("=" * 40)
    
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check admin_logs table
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='admin_logs'")
            if cursor.fetchone():
                print("✅ admin_logs table exists")
                
                # Check table structure
                cursor.execute("PRAGMA table_info(admin_logs)")
                columns = cursor.fetchall()
                print(f"📋 admin_logs columns:")
                for col in columns:
                    print(f"  - {col[1]} ({col[2]})")
                
                # Count records
                cursor.execute("SELECT COUNT(*) FROM admin_logs")
                count = cursor.fetchone()[0]
                print(f"📊 admin_logs record count: {count}")
            else:
                print("❌ admin_logs table not found!")
            
            # Check other important tables
            important_tables = ['apps', 'users', 'download_logs']
            for table in important_tables:
                cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone():
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"✅ {table} table: {count} records")
                else:
                    print(f"❌ {table} table not found!")
                    
    except Exception as e:
        print(f"❌ Database test failed: {e}")

if __name__ == "__main__":
    test_database_tables()
    add_test_logs()
    print(f"\n🎉 Test completed! Check admin logs page at: http://localhost:5000/admin/logs")
