#!/usr/bin/env python3
"""
Database Verification and Fix Script
This script will:
1. Verify database integrity
2. Test all model methods
3. Fix any data inconsistencies
4. Update rating caches
"""

import os
import sqlite3
from app.models import App, User, AppRating, get_db

def verify_database_structure():
    """Verify database structure"""
    print("🔍 Verifying database structure...")
    
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Check if all required tables exist
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = [
                'users', 'apps', 'app_ratings', 'screenshots', 
                'download_logs', 'admin_logs', 'login_logs', 'fingerprints'
            ]
            
            missing_tables = [table for table in required_tables if table not in tables]
            if missing_tables:
                print(f"❌ Missing tables: {missing_tables}")
                return False
            
            print("✅ All required tables exist")
            
            # Check apps table structure
            cursor.execute("PRAGMA table_info(apps)")
            app_columns = [row[1] for row in cursor.fetchall()]
            
            required_app_columns = [
                'id', 'name', 'description', 'short_description', 'version',
                'developer', 'category', 'user_id', 'price', 'rating',
                'downloads', 'file_path', 'external_url', 'file_size',
                'is_featured', 'created_at'
            ]
            
            missing_columns = [col for col in required_app_columns if col not in app_columns]
            if missing_columns:
                print(f"❌ Missing app columns: {missing_columns}")
                return False
            
            print("✅ Apps table structure is correct")
            return True
            
    except Exception as e:
        print(f"❌ Database structure verification failed: {e}")
        return False

def test_model_methods():
    """Test all model methods"""
    print("🧪 Testing model methods...")
    
    try:
        # Test App model methods
        apps = App.get_all(limit=5)
        print(f"✅ App.get_all() - Retrieved {len(apps)} apps")
        
        if apps:
            app = apps[0]
            app_id = app['id']
            
            # Test rating methods
            avg_rating = App.get_average_rating(app_id)
            rating_count = App.get_rating_count(app_id)
            print(f"✅ Rating methods - Avg: {avg_rating}, Count: {rating_count}")
            
            # Test utility methods
            file_size_formatted = App.get_file_size_formatted(app.get('file_size', 0))
            has_file = App.has_file(app)
            download_url = App.get_download_url(app_id)
            is_external = App.is_external_download(app)
            
            print(f"✅ Utility methods - Size: {file_size_formatted}, Has file: {has_file}")
            print(f"✅ Download methods - URL: {download_url}, External: {is_external}")
        
        # Test featured apps
        featured = App.get_featured(limit=3)
        print(f"✅ App.get_featured() - Retrieved {len(featured)} featured apps")
        
        # Test categories
        categories = App.get_categories()
        print(f"✅ App.get_categories() - Found {len(categories)} categories")
        
        # Test count
        total_count = App.get_count()
        print(f"✅ App.get_count() - Total apps: {total_count}")
        
        # Test User methods
        users = User.get_all()
        print(f"✅ User.get_all() - Retrieved {len(users)} users")
        
        if users:
            user = users[0]
            user_stats = User.get_stats(user['id'])
            print(f"✅ User.get_stats() - {user_stats}")
        
        # Test AppRating methods
        if apps:
            ratings = AppRating.get_by_app_id(apps[0]['id'])
            print(f"✅ AppRating.get_by_app_id() - Retrieved {len(ratings)} ratings")
            
            rating_dist = AppRating.get_rating_distribution(apps[0]['id'])
            print(f"✅ AppRating.get_rating_distribution() - {rating_dist}")
        
        print("🎉 All model methods working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Model method testing failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def update_rating_caches():
    """Update all app rating caches"""
    print("🔄 Updating rating caches...")
    
    try:
        apps = App.get_all()
        updated_count = 0
        
        for app in apps:
            app_id = app['id']
            App.update_rating_cache(app_id)
            updated_count += 1
        
        print(f"✅ Updated rating cache for {updated_count} apps")
        return True
        
    except Exception as e:
        print(f"❌ Rating cache update failed: {e}")
        return False

def fix_data_inconsistencies():
    """Fix any data inconsistencies"""
    print("🔧 Fixing data inconsistencies...")
    
    try:
        with get_db() as conn:
            cursor = conn.cursor()
            
            # Fix NULL values in important fields
            cursor.execute("UPDATE apps SET downloads = 0 WHERE downloads IS NULL")
            cursor.execute("UPDATE apps SET rating = 0.0 WHERE rating IS NULL")
            cursor.execute("UPDATE apps SET price = 0.0 WHERE price IS NULL")
            cursor.execute("UPDATE apps SET file_size = 0 WHERE file_size IS NULL")
            cursor.execute("UPDATE apps SET is_featured = 0 WHERE is_featured IS NULL")
            
            # Fix user login counts
            cursor.execute("UPDATE users SET login_count = 0 WHERE login_count IS NULL")
            
            conn.commit()
            
            print("✅ Fixed data inconsistencies")
            return True
            
    except Exception as e:
        print(f"❌ Data inconsistency fix failed: {e}")
        return False

def verify_template_compatibility():
    """Verify template compatibility"""
    print("🎨 Verifying template compatibility...")
    
    try:
        # Test that we can get all required data for templates
        apps = App.get_all(limit=1)
        if apps:
            app = apps[0]
            app_id = app['id']
            
            # Test all data that templates need
            avg_rating = App.get_average_rating(app_id)
            rating_count = App.get_rating_count(app_id)
            rating_distribution = AppRating.get_rating_distribution(app_id)
            
            # Verify no method calls on dict objects
            assert isinstance(avg_rating, (int, float))
            assert isinstance(rating_count, int)
            assert isinstance(rating_distribution, dict)
            
            print("✅ Template compatibility verified")
            return True
        else:
            print("⚠️  No apps found for template testing")
            return True
            
    except Exception as e:
        print(f"❌ Template compatibility verification failed: {e}")
        return False

def main():
    """Main verification function"""
    print("🚀 Starting Database Verification and Fix")
    print("=" * 50)
    
    tests = [
        ("Database Structure", verify_database_structure),
        ("Model Methods", test_model_methods),
        ("Data Inconsistencies", fix_data_inconsistencies),
        ("Rating Caches", update_rating_caches),
        ("Template Compatibility", verify_template_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"❌ {test_name} failed!")
    
    print("\n" + "=" * 50)
    print(f"📊 Verification Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Database is fully verified and ready!")
        print("\n✅ All issues have been resolved:")
        print("- Template method call errors fixed")
        print("- Database structure verified")
        print("- Model methods working correctly")
        print("- Rating caches updated")
        print("- Data inconsistencies resolved")
    else:
        print("❌ Some verification tests failed. Please check the errors above.")
        return False
    
    return True

if __name__ == '__main__':
    main()
