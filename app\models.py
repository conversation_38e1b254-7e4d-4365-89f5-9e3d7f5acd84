import sqlite3
import os
import hashlib
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from contextlib import contextmanager

# Database path
DATABASE_PATH = os.path.join('app', 'database.db')

@contextmanager
def get_db():
    """Get database connection with context manager"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # Enable dict-like access
    try:
        yield conn
    finally:
        conn.close()

def init_database():
    """Initialize database with all required tables"""
    with get_db() as conn:
        cursor = conn.cursor()

        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'publisher',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')

        # Apps table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS apps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                short_description TEXT NOT NULL,
                version TEXT NOT NULL,
                developer TEXT NOT NULL,
                uploaded_by TEXT NOT NULL DEFAULT 'Admin',
                category TEXT NOT NULL,
                price REAL DEFAULT 0.0,
                rating REAL DEFAULT 0.0,
                downloads INTEGER DEFAULT 0,
                file_path TEXT,
                external_url TEXT,
                file_size INTEGER DEFAULT 0,
                icon_path TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_featured BOOLEAN DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Screenshots table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS screenshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                file_path TEXT NOT NULL,
                caption TEXT,
                order_num INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (app_id) REFERENCES apps (id) ON DELETE CASCADE
            )
        ''')

        # Download logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS download_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (app_id) REFERENCES apps (id)
            )
        ''')

        # Admin logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Login logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                failure_reason TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # App ratings table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS app_ratings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                user_id INTEGER,
                rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
                review TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT,
                fingerprint_id TEXT,
                FOREIGN KEY (app_id) REFERENCES apps (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Login attempts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ip_address TEXT NOT NULL,
                username TEXT,
                success BOOLEAN NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_agent TEXT
            )
        ''')

        # Session tokens table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS session_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                token_hash TEXT NOT NULL UNIQUE,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Fingerprints table (simplified)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fingerprints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                fingerprint TEXT UNIQUE NOT NULL,
                hmac_key TEXT NOT NULL,
                browser_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        conn.commit()

        # Create default admin user if not exists
        cursor.execute('SELECT COUNT(*) FROM users WHERE role = ?', ('admin',))
        if cursor.fetchone()[0] == 0:
            admin_password_hash = generate_password_hash('admin123')
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ('0xmrpepe', '<EMAIL>', admin_password_hash, 'admin', 1))
            conn.commit()
            print("✓ Default admin user created (username: 0xmrpepe, password: admin123)")

        print("✓ Database initialized successfully")

class User:
    """User model with raw sqlite3 operations"""

    @staticmethod
    def create(username, email, password, role='publisher'):
        """Create a new user"""
        password_hash = generate_password_hash(password)
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, email, password_hash, role, 1))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_id(user_id):
        """Get user by ID"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
            row = cursor.fetchone()
            return dict(row) if row else None

    @staticmethod
    def get_by_username(username):
        """Get user by username"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
            row = cursor.fetchone()
            return dict(row) if row else None

    @staticmethod
    def get_by_email(email):
        """Get user by email"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE email = ?', (email,))
            row = cursor.fetchone()
            return dict(row) if row else None

    @staticmethod
    def check_password(user, password):
        """Check if password is correct"""
        if not user:
            return False
        return check_password_hash(user['password_hash'], password)

    @staticmethod
    def update_last_login(user_id):
        """Update last login timestamp"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
            ''', (user_id,))
            conn.commit()

    @staticmethod
    def is_admin(user):
        """Check if user is admin"""
        return user and user.get('role') == 'admin'

    @staticmethod
    def is_publisher(user):
        """Check if user is publisher or admin"""
        return user and user.get('role') in ['admin', 'publisher']

    @staticmethod
    def get_all():
        """Get all users"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users ORDER BY created_at DESC')
            return [dict(row) for row in cursor.fetchall()]

    @staticmethod
    def update(user_id, **kwargs):
        """Update user fields"""
        if not kwargs:
            return False

        # Build dynamic update query
        fields = []
        values = []
        for key, value in kwargs.items():
            if key in ['username', 'email', 'role', 'is_active']:
                fields.append(f"{key} = ?")
                values.append(value)

        if not fields:
            return False

        values.append(user_id)
        query = f"UPDATE users SET {', '.join(fields)} WHERE id = ?"

        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute(query, values)
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def delete(user_id):
        """Delete user"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
            conn.commit()
            return cursor.rowcount > 0

class App:
    """App model with raw sqlite3 operations"""

    @staticmethod
    def create(name, description, short_description, version, developer, category, user_id, **kwargs):
        """Create a new app"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO apps (name, description, short_description, version, developer,
                                category, user_id, uploaded_by, price, file_path, external_url,
                                file_size, icon_path, is_featured)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                name, description, short_description, version, developer, category, user_id,
                kwargs.get('uploaded_by', 'Admin'),
                kwargs.get('price', 0.0),
                kwargs.get('file_path'),
                kwargs.get('external_url'),
                kwargs.get('file_size', 0),
                kwargs.get('icon_path'),
                kwargs.get('is_featured', 0)
            ))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_id(app_id):
        """Get app by ID"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM apps WHERE id = ?', (app_id,))
            row = cursor.fetchone()
            return dict(row) if row else None

    @staticmethod
    def get_all(limit=None, offset=0, category=None, search=None, featured_only=False):
        """Get all apps with optional filtering"""
        with get_db() as conn:
            cursor = conn.cursor()

            query = 'SELECT * FROM apps WHERE 1=1'
            params = []

            if category:
                query += ' AND category = ?'
                params.append(category)

            if search:
                query += ' AND (name LIKE ? OR description LIKE ? OR developer LIKE ?)'
                search_term = f'%{search}%'
                params.extend([search_term, search_term, search_term])

            if featured_only:
                query += ' AND is_featured = 1'

            query += ' ORDER BY created_at DESC'

            if limit:
                query += ' LIMIT ? OFFSET ?'
                params.extend([limit, offset])

            cursor.execute(query, params)
            return [dict(row) for row in cursor.fetchall()]

    @staticmethod
    def get_categories():
        """Get all unique categories"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT DISTINCT category FROM apps ORDER BY category')
            return [row[0] for row in cursor.fetchall()]

    @staticmethod
    def increment_downloads(app_id):
        """Increment download count"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('UPDATE apps SET downloads = downloads + 1 WHERE id = ?', (app_id,))
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def update(app_id, **kwargs):
        """Update app fields"""
        if not kwargs:
            return False

        # Build dynamic update query
        fields = []
        values = []
        allowed_fields = ['name', 'description', 'short_description', 'version', 'developer',
                         'category', 'price', 'file_path', 'external_url', 'file_size',
                         'icon_path', 'is_featured']

        for key, value in kwargs.items():
            if key in allowed_fields:
                fields.append(f"{key} = ?")
                values.append(value)

        if not fields:
            return False

        # Add updated_at timestamp
        fields.append("updated_at = CURRENT_TIMESTAMP")
        values.append(app_id)

        query = f"UPDATE apps SET {', '.join(fields)} WHERE id = ?"

        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute(query, values)
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def delete(app_id):
        """Delete app and related data"""
        with get_db() as conn:
            cursor = conn.cursor()
            # Delete related screenshots first
            cursor.execute('DELETE FROM screenshots WHERE app_id = ?', (app_id,))
            # Delete the app
            cursor.execute('DELETE FROM apps WHERE id = ?', (app_id,))
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def get_file_size_formatted(file_size):
        """Return formatted file size"""
        if not file_size:
            return "0 B"

        size = float(file_size)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    @staticmethod
    def has_file(app):
        """Check if app has a downloadable file"""
        return bool(app.get('file_path') or app.get('external_url'))

    @staticmethod
    def get_download_url(app_id):
        """Get the download URL"""
        return f"/download/{app_id}"

    @staticmethod
    def is_external_download(app):
        """Check if download is external"""
        return bool(app.get('external_url'))

    @staticmethod
    def get_average_rating(app_id):
        """Calculate average rating"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT AVG(rating) FROM app_ratings WHERE app_id = ?', (app_id,))
            result = cursor.fetchone()
            return result[0] if result[0] else 0.0

    @staticmethod
    def get_rating_count(app_id):
        """Get total number of ratings"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM app_ratings WHERE app_id = ?', (app_id,))
            return cursor.fetchone()[0]

class Screenshot:
    """Screenshot model with raw sqlite3 operations"""

    @staticmethod
    def create(app_id, file_path, caption=None, order_num=0):
        """Create a new screenshot"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO screenshots (app_id, file_path, caption, order_num)
                VALUES (?, ?, ?, ?)
            ''', (app_id, file_path, caption, order_num))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_app_id(app_id):
        """Get all screenshots for an app"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM screenshots WHERE app_id = ? ORDER BY order_num', (app_id,))
            return [dict(row) for row in cursor.fetchall()]

    @staticmethod
    def delete(screenshot_id):
        """Delete screenshot"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM screenshots WHERE id = ?', (screenshot_id,))
            conn.commit()
            return cursor.rowcount > 0

class DownloadLog:
    """Download log model with raw sqlite3 operations"""

    @staticmethod
    def create(app_id, ip_address, user_agent=None):
        """Log a download"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO download_logs (app_id, ip_address, user_agent)
                VALUES (?, ?, ?)
            ''', (app_id, ip_address, user_agent))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_app_id(app_id, limit=100):
        """Get download logs for an app"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM download_logs WHERE app_id = ?
                ORDER BY timestamp DESC LIMIT ?
            ''', (app_id, limit))
            return [dict(row) for row in cursor.fetchall()]

    @staticmethod
    def get_all(limit=100):
        """Get all download logs"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM download_logs ORDER BY timestamp DESC LIMIT ?
            ''', (limit,))
            return [dict(row) for row in cursor.fetchall()]

class AdminLog:
    """Admin log model with raw sqlite3 operations"""

    @staticmethod
    def create(user_id, username, action, details, ip_address, user_agent=None):
        """Create admin log entry"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO admin_logs (user_id, username, action, details, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, username, action, details, ip_address, user_agent))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_all(limit=100):
        """Get all admin logs"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM admin_logs ORDER BY timestamp DESC LIMIT ?
            ''', (limit,))
            return [dict(row) for row in cursor.fetchall()]

class LoginLog:
    """Login log model with raw sqlite3 operations"""

    @staticmethod
    def create(user_id, username, success, ip_address, user_agent=None, failure_reason=None):
        """Create login log entry"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO login_logs (user_id, username, success, ip_address, user_agent, failure_reason)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, username, success, ip_address, user_agent, failure_reason))
            conn.commit()
            return cursor.lastrowid

class AppRating:
    """App rating model with raw sqlite3 operations"""

    @staticmethod
    def create(app_id, rating, user_id=None, review=None, ip_address=None, fingerprint_id=None):
        """Create app rating"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO app_ratings (app_id, user_id, rating, review, ip_address, fingerprint_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (app_id, user_id, rating, review, ip_address, fingerprint_id))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_app_id(app_id):
        """Get all ratings for an app"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM app_ratings WHERE app_id = ? ORDER BY timestamp DESC', (app_id,))
            return [dict(row) for row in cursor.fetchall()]

class Fingerprint:
    """Fingerprint model with raw sqlite3 operations"""

    @staticmethod
    def create(uuid_val, fingerprint, hmac_key, browser_data=None):
        """Create fingerprint entry"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO fingerprints (uuid, fingerprint, hmac_key, browser_data)
                VALUES (?, ?, ?, ?)
            ''', (uuid_val, fingerprint, hmac_key, browser_data))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_fingerprint(fingerprint):
        """Get fingerprint by fingerprint value"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM fingerprints WHERE fingerprint = ?', (fingerprint,))
            row = cursor.fetchone()
            return dict(row) if row else None

    @staticmethod
    def get_by_uuid(uuid_val):
        """Get fingerprint by UUID"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM fingerprints WHERE uuid = ?', (uuid_val,))
            row = cursor.fetchone()
            return dict(row) if row else None
