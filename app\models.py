import sqlite3
import os
import hashlib
import hmac
import secrets
import json
from datetime import datetime, timedelta
from werkzeug.security import generate_password_hash, check_password_hash
from contextlib import contextmanager
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

# Database path - ensure it's in the app directory
DATABASE_PATH = os.path.join(os.path.dirname(__file__), 'database.db')

@contextmanager
def get_db():
    """Get database connection with context manager and datetime parsing"""
    conn = sqlite3.connect(DATABASE_PATH, detect_types=sqlite3.PARSE_DECLTYPES|sqlite3.PARSE_COLNAMES)
    conn.row_factory = sqlite3.Row  # Enable dict-like access
    try:
        yield conn
    finally:
        conn.close()

def convert_row_to_dict(row):
    """Convert sqlite3.Row to dict with proper datetime handling"""
    if not row:
        return None

    result = dict(row)

    # Convert timestamp strings to datetime objects for common fields
    datetime_fields = ['created_at', 'updated_at', 'timestamp', 'last_login', 'published_at', 'reviewed_at', 'expires_at', 'last_edited', 'clicked_at']

    for field in datetime_fields:
        if field in result and result[field]:
            if isinstance(result[field], str):
                try:
                    # Try different timestamp formats
                    for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']:
                        try:
                            result[field] = datetime.strptime(result[field], fmt)
                            break
                        except ValueError:
                            continue
                except Exception:
                    # If all parsing fails, keep original string
                    pass

    return result

def init_database():
    """Initialize database with all required tables"""
    with get_db() as conn:
        cursor = conn.cursor()

        # Users table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                email TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                role TEXT NOT NULL DEFAULT 'publisher',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')

        # Apps table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS apps (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT NOT NULL,
                short_description TEXT NOT NULL,
                version TEXT NOT NULL,
                developer TEXT NOT NULL,
                uploaded_by TEXT NOT NULL DEFAULT 'Admin',
                category TEXT NOT NULL,
                price REAL DEFAULT 0.0,
                rating REAL DEFAULT 0.0,
                downloads INTEGER DEFAULT 0,
                file_path TEXT,
                external_url TEXT,
                file_size INTEGER DEFAULT 0,
                icon_path TEXT,
                user_id INTEGER NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_featured BOOLEAN DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Screenshots table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS screenshots (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                file_path TEXT NOT NULL,
                caption TEXT,
                order_num INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (app_id) REFERENCES apps (id) ON DELETE CASCADE
            )
        ''')

        # Download logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS download_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (app_id) REFERENCES apps (id)
            )
        ''')

        # Admin logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS admin_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                action TEXT NOT NULL,
                details TEXT,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Login logs table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                success BOOLEAN NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                failure_reason TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # App ratings table (enhanced)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS app_ratings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                user_id INTEGER,
                rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
                review TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_edited TIMESTAMP,
                edit_count INTEGER DEFAULT 0,
                ip_address TEXT,
                fingerprint_id TEXT,
                is_verified BOOLEAN DEFAULT 0,
                helpful_count INTEGER DEFAULT 0,
                FOREIGN KEY (app_id) REFERENCES apps (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Login attempts table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS login_attempts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                ip_address TEXT NOT NULL,
                username TEXT,
                success BOOLEAN NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_agent TEXT
            )
        ''')

        # Session tokens table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS session_tokens (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                token_hash TEXT NOT NULL UNIQUE,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_used TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP NOT NULL,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Fingerprints table (simplified)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS fingerprints (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                uuid TEXT UNIQUE NOT NULL,
                fingerprint TEXT UNIQUE NOT NULL,
                hmac_key TEXT NOT NULL,
                browser_data TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # Abuse reports table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS abuse_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                app_id INTEGER NOT NULL,
                user_id INTEGER,
                report_type TEXT NOT NULL,
                reason TEXT NOT NULL,
                description TEXT,
                encrypted_data TEXT,
                signature TEXT,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                status TEXT DEFAULT 'pending',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reviewed_at TIMESTAMP,
                reviewed_by INTEGER,
                FOREIGN KEY (app_id) REFERENCES apps (id),
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (reviewed_by) REFERENCES users (id)
            )
        ''')

        # Suggestions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS suggestions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                category TEXT NOT NULL,
                title TEXT NOT NULL,
                description TEXT NOT NULL,
                encrypted_data TEXT,
                signature TEXT,
                priority TEXT DEFAULT 'normal',
                status TEXT DEFAULT 'pending',
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                reviewed_at TIMESTAMP,
                reviewed_by INTEGER,
                admin_response TEXT,
                FOREIGN KEY (user_id) REFERENCES users (id),
                FOREIGN KEY (reviewed_by) REFERENCES users (id)
            )
        ''')

        # Rating edit history table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS rating_edit_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                rating_id INTEGER NOT NULL,
                old_rating INTEGER NOT NULL,
                new_rating INTEGER NOT NULL,
                old_review TEXT,
                new_review TEXT,
                edit_reason TEXT,
                edited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (rating_id) REFERENCES app_ratings (id)
            )
        ''')

        # Security keys table for encryption
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS security_keys (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key_name TEXT UNIQUE NOT NULL,
                key_data TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at TIMESTAMP,
                is_active BOOLEAN DEFAULT 1
            )
        ''')

        # Shortlinks table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS shortlinks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                short_code TEXT UNIQUE NOT NULL,
                original_url TEXT NOT NULL,
                app_id INTEGER,
                user_id INTEGER NOT NULL,
                title TEXT,
                description TEXT,
                click_count INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                expires_at TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (app_id) REFERENCES apps (id),
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Shortlink analytics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS shortlink_clicks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                shortlink_id INTEGER NOT NULL,
                ip_address TEXT NOT NULL,
                user_agent TEXT,
                referer TEXT,
                country TEXT,
                clicked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (shortlink_id) REFERENCES shortlinks (id)
            )
        ''')

        # Posts table for publishers
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS posts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                content TEXT NOT NULL,
                excerpt TEXT,
                featured_image TEXT,
                category TEXT DEFAULT 'general',
                tags TEXT,
                status TEXT DEFAULT 'draft',
                is_featured BOOLEAN DEFAULT 0,
                view_count INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                published_at TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        conn.commit()

        # Create default admin user if not exists
        cursor.execute('SELECT COUNT(*) FROM users WHERE role = ?', ('admin',))
        if cursor.fetchone()[0] == 0:
            admin_password_hash = generate_password_hash('admin123')
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', ('0xmrpepe', '<EMAIL>', admin_password_hash, 'admin', 1))
            conn.commit()
            print("✓ Default admin user created (username: 0xmrpepe, password: admin123)")

        print("✓ Database initialized successfully")

class SecurityManager:
    """Handle encryption, signing, and security operations"""

    @staticmethod
    def get_or_create_key(key_name='main_encryption_key'):
        """Get or create encryption key"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT key_data FROM security_keys WHERE key_name = ? AND is_active = 1', (key_name,))
            row = cursor.fetchone()

            if row:
                return row[0].encode()
            else:
                # Generate new key
                key = Fernet.generate_key()
                cursor.execute('''
                    INSERT INTO security_keys (key_name, key_data, expires_at)
                    VALUES (?, ?, ?)
                ''', (key_name, key.decode(), datetime.now() + timedelta(days=365)))
                conn.commit()
                return key

    @staticmethod
    def encrypt_data(data, key_name='main_encryption_key'):
        """Encrypt sensitive data"""
        try:
            key = SecurityManager.get_or_create_key(key_name)
            fernet = Fernet(key)

            if isinstance(data, str):
                data = data.encode()
            elif isinstance(data, dict):
                data = json.dumps(data).encode()

            encrypted = fernet.encrypt(data)
            return base64.b64encode(encrypted).decode()
        except Exception:
            return None

    @staticmethod
    def decrypt_data(encrypted_data, key_name='main_encryption_key'):
        """Decrypt sensitive data"""
        try:
            key = SecurityManager.get_or_create_key(key_name)
            fernet = Fernet(key)

            encrypted_bytes = base64.b64decode(encrypted_data.encode())
            decrypted = fernet.decrypt(encrypted_bytes)

            try:
                # Try to parse as JSON
                return json.loads(decrypted.decode())
            except json.JSONDecodeError:
                # Return as string
                return decrypted.decode()
        except Exception:
            return None

    @staticmethod
    def sign_data(data, secret_key=None):
        """Create HMAC signature for data integrity"""
        if secret_key is None:
            secret_key = SecurityManager.get_or_create_key('signing_key')

        if isinstance(data, str):
            data = data.encode()
        elif isinstance(data, dict):
            data = json.dumps(data, sort_keys=True).encode()

        signature = hmac.new(secret_key, data, hashlib.sha256).hexdigest()
        return signature

    @staticmethod
    def verify_signature(data, signature, secret_key=None):
        """Verify HMAC signature"""
        try:
            expected_signature = SecurityManager.sign_data(data, secret_key)
            return hmac.compare_digest(signature, expected_signature)
        except Exception:
            return False

    @staticmethod
    def hash_data(data, salt=None):
        """Create secure hash of data"""
        if salt is None:
            salt = secrets.token_bytes(32)

        if isinstance(data, str):
            data = data.encode()

        # Use PBKDF2 for secure hashing
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        hash_value = kdf.derive(data)

        return {
            'hash': base64.b64encode(hash_value).decode(),
            'salt': base64.b64encode(salt).decode()
        }

class User:
    """User model with raw sqlite3 operations"""

    @staticmethod
    def create(username, email, password, role='publisher'):
        """Create a new user"""
        password_hash = generate_password_hash(password)
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO users (username, email, password_hash, role, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, email, password_hash, role, 1))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_id(user_id):
        """Get user by ID"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE id = ?', (user_id,))
            row = cursor.fetchone()
            return convert_row_to_dict(row)

    @staticmethod
    def get_by_username(username):
        """Get user by username"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE username = ?', (username,))
            row = cursor.fetchone()
            return convert_row_to_dict(row)

    @staticmethod
    def get_by_email(email):
        """Get user by email"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users WHERE email = ?', (email,))
            row = cursor.fetchone()
            return convert_row_to_dict(row)

    @staticmethod
    def check_password(user, password):
        """Check if password is correct"""
        if not user:
            return False
        return check_password_hash(user['password_hash'], password)

    @staticmethod
    def update_last_login(user_id):
        """Update last login timestamp and increment login count"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP, login_count = login_count + 1 WHERE id = ?
            ''', (user_id,))
            conn.commit()

    @staticmethod
    def get_stats(user_id):
        """Get user statistics"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Get app count
            cursor.execute('SELECT COUNT(*) FROM apps WHERE user_id = ?', (user_id,))
            app_count = cursor.fetchone()[0]

            # Get total downloads
            cursor.execute('''
                SELECT SUM(downloads) FROM apps WHERE user_id = ?
            ''', (user_id,))
            total_downloads = cursor.fetchone()[0] or 0

            # Get average rating
            cursor.execute('''
                SELECT AVG(CAST(rating AS FLOAT)) FROM apps WHERE user_id = ? AND rating > 0
            ''', (user_id,))
            avg_rating = cursor.fetchone()[0] or 0.0

            return {
                'app_count': app_count,
                'total_downloads': total_downloads,
                'avg_rating': round(avg_rating, 1) if avg_rating else 0.0
            }

    @staticmethod
    def is_admin(user):
        """Check if user is admin"""
        return user and user.get('role') == 'admin'

    @staticmethod
    def is_publisher(user):
        """Check if user is publisher or admin"""
        return user and user.get('role') in ['admin', 'publisher']

    @staticmethod
    def get_all():
        """Get all users"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM users ORDER BY created_at DESC')
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def update(user_id, **kwargs):
        """Update user fields"""
        if not kwargs:
            return False

        # Build dynamic update query
        fields = []
        values = []
        for key, value in kwargs.items():
            if key in ['username', 'email', 'role', 'is_active']:
                fields.append(f"{key} = ?")
                values.append(value)

        if not fields:
            return False

        values.append(user_id)
        query = f"UPDATE users SET {', '.join(fields)} WHERE id = ?"

        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute(query, values)
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def delete(user_id):
        """Delete user"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM users WHERE id = ?', (user_id,))
            conn.commit()
            return cursor.rowcount > 0

class App:
    """App model with raw sqlite3 operations"""

    @staticmethod
    def create(name, description, short_description, version, developer, category, user_id, **kwargs):
        """Create a new app"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO apps (name, description, short_description, version, developer,
                                category, user_id, uploaded_by, price, file_path, external_url,
                                file_size, icon_path, is_featured)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                name, description, short_description, version, developer, category, user_id,
                kwargs.get('uploaded_by', 'Admin'),
                kwargs.get('price', 0.0),
                kwargs.get('file_path'),
                kwargs.get('external_url'),
                kwargs.get('file_size', 0),
                kwargs.get('icon_path'),
                kwargs.get('is_featured', 0)
            ))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_id(app_id):
        """Get app by ID"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM apps WHERE id = ?', (app_id,))
            row = cursor.fetchone()
            return convert_row_to_dict(row)

    @staticmethod
    def get_all(limit=None, offset=0, category=None, search=None, featured_only=False):
        """Get all apps with optional filtering"""
        with get_db() as conn:
            cursor = conn.cursor()

            query = 'SELECT * FROM apps WHERE 1=1'
            params = []

            if category and category.strip():
                query += ' AND category = ?'
                params.append(category.strip())

            if search and search.strip():
                query += ' AND (name LIKE ? OR description LIKE ? OR developer LIKE ?)'
                search_term = f'%{search.strip()}%'
                params.extend([search_term, search_term, search_term])

            if featured_only:
                query += ' AND is_featured = 1'

            query += ' ORDER BY created_at DESC'

            if limit and limit > 0:
                query += ' LIMIT ?'
                params.append(limit)
                if offset > 0:
                    query += ' OFFSET ?'
                    params.append(offset)

            cursor.execute(query, params)
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def get_categories():
        """Get all unique categories"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT DISTINCT category FROM apps ORDER BY category')
            return [row[0] for row in cursor.fetchall()]

    @staticmethod
    def get_count(category=None, search=None, featured_only=False):
        """Get total count of apps with optional filtering"""
        with get_db() as conn:
            cursor = conn.cursor()

            query = 'SELECT COUNT(*) FROM apps WHERE 1=1'
            params = []

            if category and category.strip():
                query += ' AND category = ?'
                params.append(category.strip())

            if search and search.strip():
                query += ' AND (name LIKE ? OR description LIKE ? OR developer LIKE ?)'
                search_term = f'%{search.strip()}%'
                params.extend([search_term, search_term, search_term])

            if featured_only:
                query += ' AND is_featured = 1'

            cursor.execute(query, params)
            return cursor.fetchone()[0]

    @staticmethod
    def increment_downloads(app_id):
        """Increment download count"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('UPDATE apps SET downloads = downloads + 1 WHERE id = ?', (app_id,))
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def update(app_id, **kwargs):
        """Update app fields"""
        if not kwargs:
            return False

        # Build dynamic update query
        fields = []
        values = []
        allowed_fields = ['name', 'description', 'short_description', 'version', 'developer',
                         'category', 'price', 'file_path', 'external_url', 'file_size',
                         'icon_path', 'is_featured']

        for key, value in kwargs.items():
            if key in allowed_fields:
                fields.append(f"{key} = ?")
                values.append(value)

        if not fields:
            return False

        # Add updated_at timestamp
        fields.append("updated_at = CURRENT_TIMESTAMP")
        values.append(app_id)

        query = f"UPDATE apps SET {', '.join(fields)} WHERE id = ?"

        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute(query, values)
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def delete(app_id):
        """Delete app and related data"""
        with get_db() as conn:
            cursor = conn.cursor()
            # Delete related screenshots first
            cursor.execute('DELETE FROM screenshots WHERE app_id = ?', (app_id,))
            # Delete the app
            cursor.execute('DELETE FROM apps WHERE id = ?', (app_id,))
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def get_file_size_formatted(file_size):
        """Return formatted file size"""
        if not file_size:
            return "0 B"

        size = float(file_size)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024.0:
                return f"{size:.1f} {unit}"
            size /= 1024.0
        return f"{size:.1f} TB"

    @staticmethod
    def has_file(app):
        """Check if app has a downloadable file"""
        return bool(app.get('file_path') or app.get('external_url'))

    @staticmethod
    def get_download_url(app_id):
        """Get the download URL"""
        return f"/download/{app_id}"

    @staticmethod
    def is_external_download(app):
        """Check if download is external"""
        return bool(app.get('external_url'))

    @staticmethod
    def get_average_rating(app_id):
        """Calculate average rating"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT AVG(CAST(rating AS FLOAT)) FROM app_ratings WHERE app_id = ?', (app_id,))
            result = cursor.fetchone()
            return round(result[0], 1) if result[0] else 0.0

    @staticmethod
    def get_rating_count(app_id):
        """Get total number of ratings"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) FROM app_ratings WHERE app_id = ?', (app_id,))
            return cursor.fetchone()[0]

    @staticmethod
    def update_rating_cache(app_id):
        """Update cached rating in apps table"""
        avg_rating = App.get_average_rating(app_id)
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('UPDATE apps SET rating = ? WHERE id = ?', (avg_rating, app_id))
            conn.commit()

    @staticmethod
    def get_by_category(category, limit=None):
        """Get apps by category"""
        with get_db() as conn:
            cursor = conn.cursor()
            query = 'SELECT * FROM apps WHERE category = ? ORDER BY created_at DESC'
            params = [category]

            if limit:
                query += ' LIMIT ?'
                params.append(limit)

            cursor.execute(query, params)
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def get_featured(limit=6):
        """Get featured apps"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM apps WHERE is_featured = 1 ORDER BY created_at DESC LIMIT ?', (limit,))
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def search(query, limit=20):
        """Search apps by name, description, or developer"""
        with get_db() as conn:
            cursor = conn.cursor()
            search_term = f'%{query}%'
            cursor.execute('''
                SELECT * FROM apps
                WHERE name LIKE ? OR description LIKE ? OR developer LIKE ? OR tags LIKE ?
                ORDER BY
                    CASE
                        WHEN name LIKE ? THEN 1
                        WHEN developer LIKE ? THEN 2
                        WHEN tags LIKE ? THEN 3
                        ELSE 4
                    END,
                    downloads DESC
                LIMIT ?
            ''', (search_term, search_term, search_term, search_term,
                  search_term, search_term, search_term, limit))
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def get_download_url(app_id):
        """Get download URL for an app"""
        try:
            from flask import url_for
            return url_for('views.download_app', app_id=app_id)
        except RuntimeError:
            # Outside application context, return relative URL
            return f'/download/{app_id}'

    @staticmethod
    def is_external_download(app):
        """Check if app uses external download"""
        return bool(app.get('external_url') and not app.get('file_path'))

    @staticmethod
    def is_paid_app(app):
        """Check if app is paid"""
        price = app.get('price', 0)
        return price is not None and float(price) > 0

    @staticmethod
    def update_icon(app_id, icon_path, user_id=None):
        """Update app icon"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Check ownership if user_id provided
            if user_id:
                cursor.execute('SELECT user_id FROM apps WHERE id = ?', (app_id,))
                row = cursor.fetchone()
                if not row or row[0] != user_id:
                    return False, "Not authorized to update this app"

            cursor.execute('UPDATE apps SET icon_path = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                         (icon_path, app_id))
            conn.commit()
            return cursor.rowcount > 0, "Icon updated successfully"

    @staticmethod
    def delete_icon(app_id, user_id=None):
        """Delete app icon"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Check ownership if user_id provided
            if user_id:
                cursor.execute('SELECT user_id FROM apps WHERE id = ?', (app_id,))
                row = cursor.fetchone()
                if not row or row[0] != user_id:
                    return False, "Not authorized to update this app"

            # Get current icon path for deletion
            cursor.execute('SELECT icon_path FROM apps WHERE id = ?', (app_id,))
            row = cursor.fetchone()
            old_icon = row[0] if row else None

            cursor.execute('UPDATE apps SET icon_path = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                         (app_id,))
            conn.commit()

            # Delete physical file
            if old_icon and os.path.exists(old_icon):
                try:
                    os.remove(old_icon)
                except:
                    pass

            return cursor.rowcount > 0, "Icon deleted successfully"

class Screenshot:
    """Screenshot model with raw sqlite3 operations"""

    @staticmethod
    def create(app_id, file_path, caption=None, order_num=0):
        """Create a new screenshot"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO screenshots (app_id, file_path, caption, order_num)
                VALUES (?, ?, ?, ?)
            ''', (app_id, file_path, caption, order_num))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_app_id(app_id):
        """Get all screenshots for an app"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM screenshots WHERE app_id = ? ORDER BY order_num', (app_id,))
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def delete(screenshot_id, user_id=None):
        """Delete screenshot"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Check ownership if user_id provided
            if user_id:
                cursor.execute('''
                    SELECT s.file_path FROM screenshots s
                    JOIN apps a ON s.app_id = a.id
                    WHERE s.id = ? AND a.user_id = ?
                ''', (screenshot_id, user_id))
                row = cursor.fetchone()
                if not row:
                    return False, "Not authorized to delete this screenshot"
                file_path = row[0]
            else:
                cursor.execute('SELECT file_path FROM screenshots WHERE id = ?', (screenshot_id,))
                row = cursor.fetchone()
                file_path = row[0] if row else None

            cursor.execute('DELETE FROM screenshots WHERE id = ?', (screenshot_id,))
            conn.commit()

            # Delete physical file
            if file_path and os.path.exists(file_path):
                try:
                    os.remove(file_path)
                except:
                    pass

            return cursor.rowcount > 0, "Screenshot deleted successfully"

    @staticmethod
    def update_caption(screenshot_id, caption, user_id=None):
        """Update screenshot caption"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Check ownership if user_id provided
            if user_id:
                cursor.execute('''
                    SELECT s.id FROM screenshots s
                    JOIN apps a ON s.app_id = a.id
                    WHERE s.id = ? AND a.user_id = ?
                ''', (screenshot_id, user_id))
                if not cursor.fetchone():
                    return False, "Not authorized to update this screenshot"

            cursor.execute('UPDATE screenshots SET caption = ? WHERE id = ?', (caption, screenshot_id))
            conn.commit()
            return cursor.rowcount > 0, "Caption updated successfully"

    @staticmethod
    def reorder(screenshot_id, new_order, user_id=None):
        """Reorder screenshot"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Check ownership if user_id provided
            if user_id:
                cursor.execute('''
                    SELECT s.app_id FROM screenshots s
                    JOIN apps a ON s.app_id = a.id
                    WHERE s.id = ? AND a.user_id = ?
                ''', (screenshot_id, user_id))
                row = cursor.fetchone()
                if not row:
                    return False, "Not authorized to reorder this screenshot"
                app_id = row[0]
            else:
                cursor.execute('SELECT app_id FROM screenshots WHERE id = ?', (screenshot_id,))
                row = cursor.fetchone()
                app_id = row[0] if row else None

            if not app_id:
                return False, "Screenshot not found"

            cursor.execute('UPDATE screenshots SET order_num = ? WHERE id = ?', (new_order, screenshot_id))
            conn.commit()
            return cursor.rowcount > 0, "Screenshot reordered successfully"

class DownloadLog:
    """Download log model with raw sqlite3 operations"""

    @staticmethod
    def create(app_id, ip_address, user_agent=None, download_type='direct'):
        """Log a download"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO download_logs (app_id, ip_address, user_agent, download_type)
                VALUES (?, ?, ?, ?)
            ''', (app_id, ip_address, user_agent, download_type))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_app_id(app_id, limit=100):
        """Get download logs for an app"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM download_logs WHERE app_id = ?
                ORDER BY timestamp DESC LIMIT ?
            ''', (app_id, limit))
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def get_all(limit=100):
        """Get all download logs"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM download_logs ORDER BY timestamp DESC LIMIT ?
            ''', (limit,))
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

class AdminLog:
    """Admin log model with raw sqlite3 operations"""

    @staticmethod
    def create(user_id, username, action, details, ip_address, user_agent=None):
        """Create admin log entry"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO admin_logs (user_id, username, action, details, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, username, action, details, ip_address, user_agent))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_all(limit=100):
        """Get all admin logs"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT * FROM admin_logs ORDER BY timestamp DESC LIMIT ?
            ''', (limit,))
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

class LoginLog:
    """Login log model with raw sqlite3 operations"""

    @staticmethod
    def create(user_id, username, success, ip_address, user_agent=None, failure_reason=None):
        """Create login log entry"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO login_logs (user_id, username, success, ip_address, user_agent, failure_reason)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, username, success, ip_address, user_agent, failure_reason))
            conn.commit()
            return cursor.lastrowid

class AppRating:
    """Enhanced app rating model with editing capabilities"""

    EDIT_TIME_LIMIT_HOURS = 24  # Users can edit ratings within 24 hours
    MAX_EDITS = 3  # Maximum number of edits allowed

    @staticmethod
    def create(app_id, rating, user_id=None, review=None, ip_address=None, fingerprint_id=None):
        """Create app rating"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO app_ratings (app_id, user_id, rating, review, ip_address, fingerprint_id, is_verified)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (app_id, user_id, rating, review, ip_address, fingerprint_id, 1 if user_id else 0))
            conn.commit()
            rating_id = cursor.lastrowid

            # Update app rating cache
            App.update_rating_cache(app_id)
            return rating_id

    @staticmethod
    def get_by_app_id(app_id, limit=None):
        """Get all ratings for an app"""
        with get_db() as conn:
            cursor = conn.cursor()
            query = '''
                SELECT ar.*, u.username
                FROM app_ratings ar
                LEFT JOIN users u ON ar.user_id = u.id
                WHERE ar.app_id = ?
                ORDER BY ar.timestamp DESC
            '''
            params = [app_id]

            if limit:
                query += ' LIMIT ?'
                params.append(limit)

            cursor.execute(query, params)
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def get_rating_distribution(app_id):
        """Get rating distribution for an app"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT rating, COUNT(*) as count
                FROM app_ratings
                WHERE app_id = ?
                GROUP BY rating
                ORDER BY rating DESC
            ''', (app_id,))
            return {row[0]: row[1] for row in cursor.fetchall()}

    @staticmethod
    def user_has_rated(app_id, user_id=None, ip_address=None):
        """Check if user has already rated this app"""
        with get_db() as conn:
            cursor = conn.cursor()
            if user_id:
                cursor.execute('SELECT id FROM app_ratings WHERE app_id = ? AND user_id = ?', (app_id, user_id))
            elif ip_address:
                cursor.execute('SELECT id FROM app_ratings WHERE app_id = ? AND ip_address = ?', (app_id, ip_address))
            else:
                return False
            row = cursor.fetchone()
            return row[0] if row else None

    @staticmethod
    def can_edit_rating(rating_id, user_id=None, ip_address=None):
        """Check if user can edit their rating"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Get rating details
            if user_id:
                cursor.execute('''
                    SELECT timestamp, edit_count, last_edited
                    FROM app_ratings
                    WHERE id = ? AND user_id = ?
                ''', (rating_id, user_id))
            else:
                cursor.execute('''
                    SELECT timestamp, edit_count, last_edited
                    FROM app_ratings
                    WHERE id = ? AND ip_address = ?
                ''', (rating_id, ip_address))

            row = cursor.fetchone()
            if not row:
                return False, "Rating not found"

            timestamp_str, edit_count, last_edited = row

            # Parse timestamp
            try:
                if isinstance(timestamp_str, str):
                    timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
                else:
                    timestamp = timestamp_str
            except:
                return False, "Invalid timestamp"

            # Check edit count limit
            if edit_count and edit_count >= AppRating.MAX_EDITS:
                return False, f"Maximum {AppRating.MAX_EDITS} edits allowed"

            # Check time limit
            time_limit = timestamp + timedelta(hours=AppRating.EDIT_TIME_LIMIT_HOURS)
            if datetime.now() > time_limit:
                return False, f"Edit time limit ({AppRating.EDIT_TIME_LIMIT_HOURS} hours) exceeded"

            return True, "Can edit"

    @staticmethod
    def update_rating(rating_id, new_rating, new_review=None, user_id=None, ip_address=None, edit_reason=None):
        """Update an existing rating"""
        # Check if user can edit
        can_edit, message = AppRating.can_edit_rating(rating_id, user_id, ip_address)
        if not can_edit:
            return False, message

        with get_db() as conn:
            cursor = conn.cursor()

            # Get current rating data
            cursor.execute('SELECT app_id, rating, review FROM app_ratings WHERE id = ?', (rating_id,))
            row = cursor.fetchone()
            if not row:
                return False, "Rating not found"

            app_id, old_rating, old_review = row

            # Record edit history
            cursor.execute('''
                INSERT INTO rating_edit_history (rating_id, old_rating, new_rating, old_review, new_review, edit_reason)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (rating_id, old_rating, new_rating, old_review, new_review, edit_reason))

            # Update rating
            cursor.execute('''
                UPDATE app_ratings
                SET rating = ?, review = ?, last_edited = CURRENT_TIMESTAMP, edit_count = COALESCE(edit_count, 0) + 1
                WHERE id = ?
            ''', (new_rating, new_review, rating_id))

            conn.commit()

            # Update app rating cache
            App.update_rating_cache(app_id)

            return True, "Rating updated successfully"

class Fingerprint:
    """Fingerprint model with raw sqlite3 operations"""

    @staticmethod
    def create(uuid_val, fingerprint, hmac_key, browser_data=None):
        """Create fingerprint entry"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO fingerprints (uuid, fingerprint, hmac_key, browser_data)
                VALUES (?, ?, ?, ?)
            ''', (uuid_val, fingerprint, hmac_key, browser_data))
            conn.commit()
            return cursor.lastrowid

    @staticmethod
    def get_by_fingerprint(fingerprint):
        """Get fingerprint by fingerprint value"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM fingerprints WHERE fingerprint = ?', (fingerprint,))
            row = cursor.fetchone()
            return convert_row_to_dict(row)

    @staticmethod
    def get_by_uuid(uuid_val):
        """Get fingerprint by UUID"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT * FROM fingerprints WHERE uuid = ?', (uuid_val,))
            row = cursor.fetchone()
            return convert_row_to_dict(row)

class AbuseReport:
    """Abuse report model with encryption and signing"""

    @staticmethod
    def create(app_id, report_type, reason, description=None, user_id=None, ip_address=None, user_agent=None):
        """Create abuse report with encryption"""
        try:
            # Prepare data for encryption
            report_data = {
                'app_id': app_id,
                'report_type': report_type,
                'reason': reason,
                'description': description,
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'ip_address': ip_address
            }

            # Encrypt sensitive data
            encrypted_data = SecurityManager.encrypt_data(report_data)
            signature = SecurityManager.sign_data(report_data)

            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO abuse_reports (app_id, user_id, report_type, reason, description,
                                             encrypted_data, signature, ip_address, user_agent)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (app_id, user_id, report_type, reason, description,
                      encrypted_data, signature, ip_address, user_agent))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"Error creating abuse report: {e}")
            return None

    @staticmethod
    def get_all(limit=50, status=None):
        """Get abuse reports"""
        with get_db() as conn:
            cursor = conn.cursor()
            query = '''
                SELECT ar.*, a.name as app_name, u.username
                FROM abuse_reports ar
                LEFT JOIN apps a ON ar.app_id = a.id
                LEFT JOIN users u ON ar.user_id = u.id
            '''
            params = []

            if status:
                query += ' WHERE ar.status = ?'
                params.append(status)

            query += ' ORDER BY ar.created_at DESC LIMIT ?'
            params.append(limit)

            cursor.execute(query, params)
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def update_status(report_id, status, reviewed_by=None):
        """Update abuse report status"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE abuse_reports
                SET status = ?, reviewed_at = CURRENT_TIMESTAMP, reviewed_by = ?
                WHERE id = ?
            ''', (status, reviewed_by, report_id))
            conn.commit()
            return cursor.rowcount > 0

class Suggestion:
    """Suggestion model with encryption and signing"""

    @staticmethod
    def create(category, title, description, user_id=None, ip_address=None, user_agent=None, priority='normal'):
        """Create suggestion with encryption"""
        try:
            # Prepare data for encryption
            suggestion_data = {
                'category': category,
                'title': title,
                'description': description,
                'timestamp': datetime.now().isoformat(),
                'user_id': user_id,
                'ip_address': ip_address,
                'priority': priority
            }

            # Encrypt sensitive data
            encrypted_data = SecurityManager.encrypt_data(suggestion_data)
            signature = SecurityManager.sign_data(suggestion_data)

            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO suggestions (user_id, category, title, description, encrypted_data,
                                           signature, priority, ip_address, user_agent)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (user_id, category, title, description, encrypted_data,
                      signature, priority, ip_address, user_agent))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"Error creating suggestion: {e}")
            return None

    @staticmethod
    def get_all(limit=50, status=None, category=None):
        """Get suggestions"""
        with get_db() as conn:
            cursor = conn.cursor()
            query = '''
                SELECT s.*, u.username
                FROM suggestions s
                LEFT JOIN users u ON s.user_id = u.id
                WHERE 1=1
            '''
            params = []

            if status:
                query += ' AND s.status = ?'
                params.append(status)

            if category:
                query += ' AND s.category = ?'
                params.append(category)

            query += ' ORDER BY s.created_at DESC LIMIT ?'
            params.append(limit)

            cursor.execute(query, params)
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def update_status(suggestion_id, status, reviewed_by=None, admin_response=None):
        """Update suggestion status"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                UPDATE suggestions
                SET status = ?, reviewed_at = CURRENT_TIMESTAMP, reviewed_by = ?, admin_response = ?
                WHERE id = ?
            ''', (status, reviewed_by, admin_response, suggestion_id))
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def get_categories():
        """Get suggestion categories"""
        return [
            'feature_request',
            'bug_report',
            'ui_improvement',
            'performance',
            'security',
            'content_request',
            'other'
        ]

class Shortlink:
    """Shortlink management system for admins and publishers"""

    @staticmethod
    def generate_short_code(length=6):
        """Generate a unique short code"""
        import string
        import random

        characters = string.ascii_letters + string.digits
        while True:
            code = ''.join(random.choice(characters) for _ in range(length))
            # Check if code already exists
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT id FROM shortlinks WHERE short_code = ?', (code,))
                if not cursor.fetchone():
                    return code

    @staticmethod
    def create(original_url, user_id, app_id=None, title=None, description=None, expires_at=None, custom_code=None):
        """Create a new shortlink"""
        try:
            # Generate or validate short code
            if custom_code:
                # Check if custom code is available
                with get_db() as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT id FROM shortlinks WHERE short_code = ?', (custom_code,))
                    if cursor.fetchone():
                        return None, "Custom code already exists"
                short_code = custom_code
            else:
                short_code = Shortlink.generate_short_code()

            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO shortlinks (short_code, original_url, app_id, user_id, title, description, expires_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (short_code, original_url, app_id, user_id, title, description, expires_at))
                conn.commit()
                return cursor.lastrowid, short_code
        except Exception as e:
            return None, str(e)

    @staticmethod
    def get_by_code(short_code):
        """Get shortlink by code"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT s.*, u.username, a.name as app_name
                FROM shortlinks s
                LEFT JOIN users u ON s.user_id = u.id
                LEFT JOIN apps a ON s.app_id = a.id
                WHERE s.short_code = ? AND s.is_active = 1
            ''', (short_code,))
            row = cursor.fetchone()
            return convert_row_to_dict(row)

    @staticmethod
    def get_by_user(user_id, limit=50):
        """Get shortlinks by user"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT s.*, a.name as app_name
                FROM shortlinks s
                LEFT JOIN apps a ON s.app_id = a.id
                WHERE s.user_id = ?
                ORDER BY s.created_at DESC
                LIMIT ?
            ''', (user_id, limit))
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def increment_clicks(shortlink_id, ip_address, user_agent=None, referer=None):
        """Record a click and increment counter"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Record click analytics
            cursor.execute('''
                INSERT INTO shortlink_clicks (shortlink_id, ip_address, user_agent, referer)
                VALUES (?, ?, ?, ?)
            ''', (shortlink_id, ip_address, user_agent, referer))

            # Increment click count
            cursor.execute('''
                UPDATE shortlinks SET click_count = click_count + 1 WHERE id = ?
            ''', (shortlink_id,))

            conn.commit()

    @staticmethod
    def get_analytics(shortlink_id):
        """Get analytics for a shortlink"""
        with get_db() as conn:
            cursor = conn.cursor()

            # Get click statistics
            cursor.execute('''
                SELECT
                    COUNT(*) as total_clicks,
                    COUNT(DISTINCT ip_address) as unique_clicks,
                    DATE(clicked_at) as click_date,
                    COUNT(*) as daily_clicks
                FROM shortlink_clicks
                WHERE shortlink_id = ?
                GROUP BY DATE(clicked_at)
                ORDER BY click_date DESC
                LIMIT 30
            ''', (shortlink_id,))

            daily_stats = [convert_row_to_dict(row) for row in cursor.fetchall()]

            # Get total stats
            cursor.execute('''
                SELECT
                    COUNT(*) as total_clicks,
                    COUNT(DISTINCT ip_address) as unique_clicks
                FROM shortlink_clicks
                WHERE shortlink_id = ?
            ''', (shortlink_id,))

            total_stats = dict(cursor.fetchone()) if cursor.fetchone() else {'total_clicks': 0, 'unique_clicks': 0}

            return {
                'total_stats': total_stats,
                'daily_stats': daily_stats
            }

    @staticmethod
    def update(shortlink_id, **kwargs):
        """Update shortlink"""
        if not kwargs:
            return False

        allowed_fields = ['title', 'description', 'is_active', 'expires_at']
        fields = []
        values = []

        for key, value in kwargs.items():
            if key in allowed_fields:
                fields.append(f"{key} = ?")
                values.append(value)

        if not fields:
            return False

        fields.append("updated_at = CURRENT_TIMESTAMP")
        values.append(shortlink_id)

        with get_db() as conn:
            cursor = conn.cursor()
            query = f"UPDATE shortlinks SET {', '.join(fields)} WHERE id = ?"
            cursor.execute(query, values)
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def delete(shortlink_id, user_id):
        """Delete shortlink (only by owner)"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM shortlinks WHERE id = ? AND user_id = ?', (shortlink_id, user_id))
            conn.commit()
            return cursor.rowcount > 0

class Post:
    """Post management system for publishers"""

    @staticmethod
    def create(user_id, title, content, excerpt=None, category='general', tags=None, status='draft'):
        """Create a new post"""
        try:
            with get_db() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO posts (user_id, title, content, excerpt, category, tags, status)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (user_id, title, content, excerpt, category, tags, status))
                conn.commit()
                return cursor.lastrowid
        except Exception as e:
            print(f"Error creating post: {e}")
            return None

    @staticmethod
    def get_by_id(post_id):
        """Get post by ID"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT p.*, u.username
                FROM posts p
                LEFT JOIN users u ON p.user_id = u.id
                WHERE p.id = ?
            ''', (post_id,))
            row = cursor.fetchone()
            return convert_row_to_dict(row)

    @staticmethod
    def get_by_user(user_id, limit=50, status=None):
        """Get posts by user"""
        with get_db() as conn:
            cursor = conn.cursor()
            query = 'SELECT * FROM posts WHERE user_id = ?'
            params = [user_id]

            if status:
                query += ' AND status = ?'
                params.append(status)

            query += ' ORDER BY created_at DESC LIMIT ?'
            params.append(limit)

            cursor.execute(query, params)
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def get_all(limit=50, status='published', category=None):
        """Get all posts"""
        with get_db() as conn:
            cursor = conn.cursor()
            query = '''
                SELECT p.*, u.username
                FROM posts p
                LEFT JOIN users u ON p.user_id = u.id
                WHERE p.status = ?
            '''
            params = [status]

            if category:
                query += ' AND p.category = ?'
                params.append(category)

            query += ' ORDER BY p.created_at DESC LIMIT ?'
            params.append(limit)

            cursor.execute(query, params)
            return [convert_row_to_dict(row) for row in cursor.fetchall()]

    @staticmethod
    def update(post_id, user_id, **kwargs):
        """Update post (only by owner)"""
        if not kwargs:
            return False

        allowed_fields = ['title', 'content', 'excerpt', 'category', 'tags', 'status', 'featured_image']
        fields = []
        values = []

        for key, value in kwargs.items():
            if key in allowed_fields:
                fields.append(f"{key} = ?")
                values.append(value)

        if not fields:
            return False

        fields.append("updated_at = CURRENT_TIMESTAMP")
        values.extend([post_id, user_id])

        with get_db() as conn:
            cursor = conn.cursor()
            query = f"UPDATE posts SET {', '.join(fields)} WHERE id = ? AND user_id = ?"
            cursor.execute(query, values)
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def delete(post_id, user_id):
        """Delete post (only by owner)"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('DELETE FROM posts WHERE id = ? AND user_id = ?', (post_id, user_id))
            conn.commit()
            return cursor.rowcount > 0

    @staticmethod
    def get_categories():
        """Get available post categories"""
        return [
            'general',
            'tutorial',
            'review',
            'news',
            'announcement',
            'guide',
            'tips',
            'security',
            'development'
        ]

    @staticmethod
    def increment_views(post_id):
        """Increment post view count"""
        with get_db() as conn:
            cursor = conn.cursor()
            cursor.execute('UPDATE posts SET view_count = view_count + 1 WHERE id = ?', (post_id,))
            conn.commit()
