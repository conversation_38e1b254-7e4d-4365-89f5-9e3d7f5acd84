{% extends "base.html" %}

{% block title %}{{ app.name }} - App Store{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('views.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('views.index', category=app.category) }}">{{ app.category }}</a></li>
            <li class="breadcrumb-item active">{{ app.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- App Info -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            {% if app.icon_path %}
                            <img src="{{ url_for('uploaded_file', filename=app.icon_path) }}"
                                 class="img-fluid rounded app-detail-icon" alt="{{ app.name }}">
                            {% else %}
                            <div class="app-detail-icon-placeholder">
                                <i class="bi bi-app"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="col-md-9">
                            <h1 class="mb-2">{{ app.name }}</h1>
                            <p class="text-muted mb-2">by {{ app.developer }}</p>
                            <p class="lead">{{ app.short_description }}</p>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Category:</strong>
                                    <span class="badge bg-secondary">{{ app.category }}</span>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Size:</strong> {{ app.file_size | file_size }}
                                </div>
                                <div class="col-sm-6">
                                    <strong>Downloads:</strong> {{ app.downloads }}
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-sm-6">
                                    <strong>Uploaded by:</strong> {{ app.uploaded_by }}
                                </div>
                                <div class="col-sm-6">
                                    <strong>Version:</strong> {{ app.version }}
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-sm-6">
                                    <h3 class="text-primary">
                                        {% if app.price == 0 %}
                                            FREE
                                        {% else %}
                                            ${{ "%.2f"|format(app.price) }}
                                        {% endif %}
                                    </h3>
                                </div>
                                <div class="col-sm-6">
                                    {% if app | has_file %}
                                        {% if app.price == 0 %}
                                            <a href="{{ app.id | download_url }}"
                                               class="btn btn-success btn-lg"
                                               {% if app | is_external %}target="_blank"{% endif %}>
                                                <i class="bi bi-download"></i>
                                                {% if app | is_external %}
                                                    Download (External)
                                                {% else %}
                                                    Download
                                                {% endif %}
                                            </a>
                                        {% else %}
                                            <a href="{{ app.id | download_url }}"
                                               class="btn btn-warning btn-lg">
                                                <i class="bi bi-credit-card"></i> Buy Now
                                            </a>
                                        {% endif %}
                                    {% else %}
                                        <button class="btn btn-secondary btn-lg" disabled>
                                            <i class="bi bi-x-circle"></i> Not Available
                                        </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Rating Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Ratings & Reviews</h3>
                </div>
                <div class="card-body">
                    <!-- Rating Summary -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="text-center">
                                <h2 class="display-4 text-primary">
                                    {% if avg_rating > 0 %}
                                        {{ "%.1f"|format(avg_rating) }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </h2>
                                <div class="rating-stars mb-2">
                                    {% for i in range(1, 6) %}
                                        {% if i <= avg_rating %}
                                            <i class="bi bi-star-fill text-warning fs-4"></i>
                                        {% elif i - 0.5 <= avg_rating %}
                                            <i class="bi bi-star-half text-warning fs-4"></i>
                                        {% else %}
                                            <i class="bi bi-star text-muted fs-4"></i>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <small class="text-muted">
                                    {% if rating_count > 0 %}
                                        {{ rating_count }} rating{{ 's' if rating_count != 1 else '' }}
                                    {% else %}
                                        No ratings yet
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Rating Distribution -->
                            {% for star in range(5, 0, -1) %}
                            <div class="d-flex align-items-center mb-1">
                                <span class="me-2">{{ star }} <i class="bi bi-star-fill text-warning"></i></span>
                                <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                    {% set star_count = rating_distribution.get(star, 0) %}
                                    {% set percentage = (star_count / rating_count * 100) if rating_count > 0 else 0 %}
                                    <div class="progress-bar bg-warning" style="width: {{ percentage }}%"></div>
                                </div>
                                <small class="text-muted">{{ star_count }}</small>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Rate This App -->
                    <div class="border-top pt-4">
                        {% if user_has_rated %}
                            {% if can_edit_rating %}
                                <h5>Edit Your Rating</h5>
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    You can edit your rating
                                    {% if edit_time_remaining %}
                                        for {{ edit_time_remaining.hours }}h {{ edit_time_remaining.minutes }}m more
                                    {% endif %}
                                </div>
                                <form method="POST" action="{{ url_for('views.rate_app', app_id=app.id) }}" id="editRatingForm">
                                    <input type="hidden" name="is_edit" value="true">
                                    <div class="mb-3">
                                        <label class="form-label">Your Rating:</label>
                                        <div class="rating-input">
                                            {% for i in range(5, 0, -1) %}
                                            <input type="radio" name="rating" value="{{ i }}" id="edit_star{{ i }}" required>
                                            <label for="edit_star{{ i }}" class="star-label">
                                                <i class="bi bi-star"></i>
                                            </label>
                                            {% endfor %}
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_review" class="form-label">Review (Optional):</label>
                                        <textarea class="form-control" id="edit_review" name="review" rows="3"
                                                placeholder="Share your experience with this app..."></textarea>
                                    </div>
                                    <div class="mb-3">
                                        <label for="edit_reason" class="form-label">Reason for Edit (Optional):</label>
                                        <input type="text" class="form-control" id="edit_reason" name="edit_reason"
                                               placeholder="Why are you updating your rating?">
                                    </div>
                                    <button type="submit" class="btn btn-warning">
                                        <i class="bi bi-pencil"></i> Update Rating
                                    </button>
                                </form>
                            {% else %}
                                <h5>Your Rating</h5>
                                <div class="alert alert-success">
                                    <i class="bi bi-check-circle"></i>
                                    Thank you for rating this app! You've already submitted your review.
                                </div>
                            {% endif %}
                        {% else %}
                            <h5>Rate This App</h5>
                            <form method="POST" action="{{ url_for('views.rate_app', app_id=app.id) }}" id="newRatingForm">
                                <div class="mb-3">
                                    <label class="form-label">Your Rating:</label>
                                    <div class="rating-input">
                                        {% for i in range(5, 0, -1) %}
                                        <input type="radio" name="rating" value="{{ i }}" id="star{{ i }}" required>
                                        <label for="star{{ i }}" class="star-label">
                                            <i class="bi bi-star"></i>
                                        </label>
                                        {% endfor %}
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="review" class="form-label">Review (Optional):</label>
                                    <textarea class="form-control" id="review" name="review" rows="3"
                                            placeholder="Share your thoughts about this app..."></textarea>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-star"></i> Submit Rating
                                </button>
                            </form>
                        {% endif %}
                    </div>

                    <!-- Report Abuse & Suggestions -->
                    <div class="border-top pt-4">
                        <h5>Report & Feedback</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-outline-danger btn-sm w-100" data-bs-toggle="modal" data-bs-target="#reportModal">
                                    <i class="bi bi-flag"></i> Report Abuse
                                </button>
                            </div>
                            <div class="col-md-6">
                                <a href="{{ url_for('views.suggestions') }}" class="btn btn-outline-info btn-sm w-100">
                                    <i class="bi bi-lightbulb"></i> Submit Suggestion
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Existing Reviews -->
                    {% if app.ratings %}
                    <div class="border-top pt-4 mt-4">
                        <h5>User Reviews</h5>
                        {% for rating in app.ratings[:5] %}
                        <div class="border-bottom pb-3 mb-3">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <div class="rating-stars">
                                        {% for i in range(1, 6) %}
                                            {% if i <= rating.rating %}
                                                <i class="bi bi-star-fill text-warning"></i>
                                            {% else %}
                                                <i class="bi bi-star text-muted"></i>
                                            {% endif %}
                                        {% endfor %}
                                    </div>
                                    <small class="text-muted">
                                        by {{ rating.user.username if rating.user else 'Anonymous' }}
                                        on {{ rating.timestamp.strftime('%B %d, %Y') }}
                                    </small>
                                </div>
                            </div>
                            {% if rating.review %}
                            <p class="mt-2 mb-0">{{ rating.review }}</p>
                            {% endif %}
                        </div>
                        {% endfor %}
                        {% if app.ratings|length > 5 %}
                        <small class="text-muted">... and {{ app.ratings|length - 5 }} more reviews</small>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Description -->
            <div class="card mt-4">
              <div class="card-header">
                <h3>Description</h3>
              </div>
              <div class="card-body">
                {{ app.description | markdown }}
              </div>
            </div>


            <!-- Screenshots -->
            {% if app.screenshots %}
            <div class="card mt-4">
                <div class="card-header">
                    <h3>Screenshots</h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for screenshot in app.screenshots %}
                        <div class="col-md-6 col-lg-4 mb-3">
                            <img src="{{ url_for('uploaded_file', filename=screenshot.file_path) }}"
                                 class="img-fluid rounded screenshot-thumb"
                                 alt="Screenshot {{ loop.index }}"
                                 data-bs-toggle="modal"
                                 data-bs-target="#screenshotModal{{ loop.index }}">
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>

            <!-- Screenshot Modals -->
            {% for screenshot in app.screenshots %}
            <div class="modal fade" id="screenshotModal{{ loop.index }}" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Screenshot {{ loop.index }}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body text-center">
                            <img src="{{ url_for('uploaded_file', filename=screenshot.file_path) }}"
                                 class="img-fluid" alt="Screenshot {{ loop.index }}">
                            {% if screenshot.caption %}
                            <p class="mt-2">{{ screenshot.caption }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- App Stats -->
            <div class="card">
                <div class="card-header">
                    <h5>App Information</h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <strong>Released:</strong><br>
                        <small class="text-muted">{{ app.created_at | datetime('%B %d, %Y') }}</small>
                    </div>
                    <div class="mb-3">
                        <strong>Last Updated:</strong><br>
                        <small class="text-muted">{{ app.updated_at | datetime('%B %d, %Y') }}</small>
                    </div>
                    {% if app.is_featured %}
                    <div class="mb-3">
                        <span class="badge bg-warning text-dark">
                            <i class="bi bi-star-fill"></i> Featured
                        </span>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Related Apps -->
            {% if related_apps %}
            <div class="card mt-4">
                <div class="card-header">
                    <h5>Related Apps</h5>
                </div>
                <div class="card-body">
                    {% for related_app in related_apps %}
                    <div class="d-flex mb-3">
                        <div class="flex-shrink-0">
                            {% if related_app.icon_path %}
                            <img src="{{ url_for('uploaded_file', filename=related_app.icon_path) }}"
                                 class="related-app-icon" alt="{{ related_app.name }}">
                            {% else %}
                            <div class="related-app-icon-placeholder">
                                <i class="bi bi-app"></i>
                            </div>
                            {% endif %}
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="mb-1">
                                <a href="{{ url_for('views.app_detail', app_id=related_app.id) }}" class="text-decoration-none">
                                    {{ related_app.name }}
                                </a>
                            </h6>
                            <p class="mb-1 small text-muted">{{ related_app.developer }}</p>
                            <small class="text-muted">
                                {% if related_app.price == 0 %}Free{% else %}&dollar;{{ "%.2f"|format(related_app.price) }}{% endif %}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Abuse Report Modal -->
<div class="modal fade" id="reportModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Report Abuse</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="reportForm">
                    <div class="mb-3">
                        <label for="reportType" class="form-label">Report Type:</label>
                        <select class="form-select" id="reportType" required>
                            <option value="">Select a reason...</option>
                            <option value="malware">Malware/Virus</option>
                            <option value="inappropriate">Inappropriate Content</option>
                            <option value="copyright">Copyright Violation</option>
                            <option value="spam">Spam/Fake App</option>
                            <option value="misleading">Misleading Information</option>
                            <option value="other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="reportReason" class="form-label">Specific Reason:</label>
                        <input type="text" class="form-control" id="reportReason" required
                               placeholder="Brief description of the issue">
                    </div>
                    <div class="mb-3">
                        <label for="reportDescription" class="form-label">Detailed Description (Optional):</label>
                        <textarea class="form-control" id="reportDescription" rows="3"
                                  placeholder="Provide additional details about the issue..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-shield-check"></i>
                        Your report will be encrypted and reviewed by our security team.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="submitReport()">Submit Report</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_head %}
<style>
.app-detail-icon {
    width: 128px;
    height: 128px;
    object-fit: cover;
}

.app-detail-icon-placeholder {
    width: 128px;
    height: 128px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #6c757d;
    border-radius: 0.375rem;
}

.screenshot-thumb {
    cursor: pointer;
    transition: transform 0.2s;
}

.screenshot-thumb:hover {
    transform: scale(1.05);
}

.related-app-icon {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 0.375rem;
}

.related-app-icon-placeholder {
    width: 48px;
    height: 48px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
    border-radius: 0.375rem;
}

/* Rating System Styles */
.rating-input {
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
}

.rating-input input[type="radio"] {
    display: none;
}

.rating-input .star-label {
    font-size: 1.5rem;
    color: #ddd;
    cursor: pointer;
    transition: color 0.2s;
    margin-right: 0.25rem;
}

.rating-input .star-label:hover,
.rating-input .star-label:hover ~ .star-label,
.rating-input input[type="radio"]:checked ~ .star-label {
    color: #ffc107;
}

.rating-input .star-label:hover {
    transform: scale(1.1);
}

.rating-stars {
    display: inline-block;
}

.rating-stars i {
    margin-right: 0.1rem;
}
</style>

<script>
function submitReport() {
    const reportType = document.getElementById('reportType').value;
    const reportReason = document.getElementById('reportReason').value;
    const reportDescription = document.getElementById('reportDescription').value;

    if (!reportType || !reportReason) {
        alert('Please fill in all required fields.');
        return;
    }

    const data = {
        report_type: reportType,
        reason: reportReason,
        description: reportDescription
    };

    fetch(`/app/{{ app.id }}/report`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            document.getElementById('reportForm').reset();
            bootstrap.Modal.getInstance(document.getElementById('reportModal')).hide();
        } else {
            alert('Error: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while submitting the report.');
    });
}
</script>
{% endblock %}
