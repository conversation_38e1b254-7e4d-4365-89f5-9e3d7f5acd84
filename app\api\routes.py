from flask import Blueprint, request, jsonify, session
from app.models import App, AppRating, User
import bleach

api = Blueprint('api', __name__)

def sanitize_input(text):
    """Sanitize user input to prevent XSS"""
    if not text:
        return ""
    return bleach.clean(text, tags=[], strip=True)

@api.route('/apps')
def get_apps():
    """Get apps via API"""
    category = request.args.get('category')
    search = request.args.get('search')
    limit = request.args.get('limit', 20, type=int)

    apps = App.get_all(limit=limit, category=category, search=search)
    return jsonify({'apps': apps})

@api.route('/app/<int:app_id>')
def get_app(app_id):
    """Get single app via API"""
    app = App.get_by_id(app_id)
    if not app:
        return jsonify({'error': 'App not found'}), 404

    return jsonify({'app': app})

@api.route('/app/<int:app_id>/rate', methods=['POST'])
def rate_app(app_id):
    """Rate an app"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    rating = data.get('rating')
    review = sanitize_input(data.get('review', ''))

    if not rating or rating < 1 or rating > 5:
        return jsonify({'error': 'Rating must be between 1 and 5'}), 400

    app = App.get_by_id(app_id)
    if not app:
        return jsonify({'error': 'App not found'}), 404

    # Create rating
    rating_id = AppRating.create(
        app_id=app_id,
        rating=rating,
        review=review,
        user_id=session.get('user_id'),
        ip_address=request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
    )

    return jsonify({'success': True, 'rating_id': rating_id})

@api.route('/stats')
def get_stats():
    """Get basic stats"""
    total_apps = len(App.get_all())
    total_users = len(User.get_all())
    categories = App.get_categories()

    return jsonify({
        'total_apps': total_apps,
        'total_users': total_users,
        'categories': categories
    })
