2025-06-10 23:24:13,425 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:24:13,426 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:24:13,433 INFO:  * Restarting with stat
2025-06-10 23:24:13,972 WARNING:  * Debugger is active!
2025-06-10 23:24:13,988 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:24:18,435 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:18] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 23:24:18,482 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:18] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:24:18,919 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:18] "GET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:24:18,920 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:18] "GET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:24:19,070 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:19] "GET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:24:19,277 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,211 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:19,453 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,547 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,562 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,885 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,138 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:33,471 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,471 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,736 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,813 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:35,875 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:35] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:36,204 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,204 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,451 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,529 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:40,238 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:26:40,239 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:26:40,246 INFO:  * Restarting with stat
2025-06-10 23:26:40,592 WARNING:  * Debugger is active!
2025-06-10 23:26:40,602 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:26:41,362 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:41] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:41,652 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:41,688 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:41,704 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:42,034 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:42] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,108 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:59,433 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,433 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,699 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,761 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,393 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:00] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:27:00,722 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,722 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,988 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,051 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,317 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:27:01,593 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,640 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,657 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,980 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,203 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:24] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:27:24,534 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,535 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,794 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:27:28,359 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:28] "[35m[1mGET /admin/apps HTTP/1.1[0m" 500 -
2025-06-10 23:27:28,685 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:27:28,690 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:27:28,947 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:27:29,008 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:29] "[36mGET /admin/apps?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:05,377 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-10 23:34:05,445 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET /gate HTTP/1.1" 200 -
2025-06-10 23:34:05,515 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 23:34:05,519 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 23:34:05,520 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-10 23:34:05,958 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "POST /registerf HTTP/1.1" 200 -
2025-06-10 23:34:05,996 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET / HTTP/1.1" 200 -
2025-06-10 23:34:06,031 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:06,032 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:08,233 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:08] "GET /login HTTP/1.1" 200 -
2025-06-10 23:34:08,258 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:08,258 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:13,273 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:13] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 23:34:13,292 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:13] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:34:13,406 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:13,406 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:16,502 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:16] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:34:16,536 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:16,536 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:19,671 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:19,696 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:34:19,696 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:34:19,708 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:34:19,743 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,363 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:36,381 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,381 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,400 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,415 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,074 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:37,089 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,090 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,099 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,106 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:54,134 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:34:54,135 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:34:54,144 INFO:  * Restarting with stat
2025-06-10 23:34:54,565 WARNING:  * Debugger is active!
2025-06-10 23:34:54,580 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:34:56,498 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:56,615 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,616 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,634 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,647 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,203 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:57,221 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,221 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,244 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,255 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:35:18,773 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:35:18,774 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:35:18,783 INFO:  * Restarting with stat
2025-06-10 23:35:19,143 WARNING:  * Debugger is active!
2025-06-10 23:35:19,152 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:35:19,207 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:19] "GET /admin/add_user HTTP/1.1" 200 -
2025-06-10 23:35:19,375 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:19,375 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:19,511 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:19] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-10 23:35:30,117 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:30] "[32mPOST /admin/add_user HTTP/1.1[0m" 302 -
2025-06-10 23:35:30,142 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:30] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:30,174 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:30,174 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,406 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:32] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:32,434 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,435 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,485 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,102 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:33,127 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,127 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,170 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,622 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:33,658 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,659 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,703 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,960 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "GET /admin/add_user HTTP/1.1" 200 -
2025-06-10 23:35:33,989 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,990 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:36,111 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:36] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:36,145 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:36,147 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:43,147 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:45,713 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:45] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:36:45,762 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:36:45,774 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:46,179 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:46] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:37:32,917 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:32] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:37:32,971 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:37:32,974 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,222 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,641 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:37:33,665 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,666 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,709 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:41:26,089 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "[35m[1mGET /admin/users HTTP/1.1[0m" 500 -
2025-06-10 23:41:26,138 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:41:26,138 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:41:26,195 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:41:26,234 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,829 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[35m[1mGET /admin/users HTTP/1.1[0m" 500 -
2025-06-10 23:42:03,855 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,855 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,879 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,896 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:43:01,963 INFO: 127.0.0.1 - - [10/Jun/2025 23:43:01] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:43:01,991 INFO: 127.0.0.1 - - [10/Jun/2025 23:43:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:43:01,991 INFO: 127.0.0.1 - - [10/Jun/2025 23:43:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:43:02,067 INFO: 127.0.0.1 - - [10/Jun/2025 23:43:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:44:27,550 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:27] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:44:27,586 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:44:27,586 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:44:29,841 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:44:29,872 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:44:29,879 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:44:29,891 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:44:29,900 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,402 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:45:21,424 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,424 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,512 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,526 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,377 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:45:22,400 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,400 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,419 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,434 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:47:40,025 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:47:40,027 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:47:40,037 INFO:  * Restarting with stat
2025-06-10 23:47:40,495 WARNING:  * Debugger is active!
2025-06-10 23:47:40,508 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:47:43,993 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:43] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:47:44,131 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,133 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,150 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,165 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:04,381 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:48:04,382 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:48:04,387 INFO:  * Restarting with stat
2025-06-10 23:48:04,762 WARNING:  * Debugger is active!
2025-06-10 23:48:04,779 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:48:08,280 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:48:08,405 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,413 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,429 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,451 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:52,251 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:48:52,252 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:48:52,258 INFO:  * Restarting with stat
2025-06-10 23:48:52,635 WARNING:  * Debugger is active!
2025-06-10 23:48:52,650 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:48:59,029 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:59] "GET /admin/logs HTTP/1.1" 200 -
2025-06-10 23:48:59,159 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:48:59,160 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:48:59,628 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:59] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:49:01,893 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:01] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:49:01,928 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:01,928 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:02,545 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:02] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:49:02,573 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:02,574 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,061 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:49:03,086 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,087 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,776 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:49:03,805 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,806 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:51:42,678 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:51:42,680 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:51:42,688 INFO:  * Restarting with stat
2025-06-10 23:51:43,077 WARNING:  * Debugger is active!
2025-06-10 23:51:43,093 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:51:47,528 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[35m[1mGET /admin/logs?type=activity HTTP/1.1[0m" 500 -
2025-06-10 23:51:47,694 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,694 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,708 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,730 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:52:25,864 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:52:25,866 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:52:25,875 INFO:  * Restarting with stat
2025-06-10 23:52:26,312 WARNING:  * Debugger is active!
2025-06-10 23:52:26,326 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:52:27,570 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:27] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:52:27,710 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:27,714 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:27,807 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:27] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:52:38,568 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:38] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:52:38,602 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:38,603 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:40,154 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:40] "GET /admin/logs HTTP/1.1" 200 -
2025-06-10 23:52:40,188 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:40,188 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:41,153 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:41] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:52:41,184 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:41,185 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,063 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[35m[1mGET /admin/logs?type=download HTTP/1.1[0m" 500 -
2025-06-10 23:52:43,105 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,108 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,130 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,153 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:55:22,423 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:55:22,426 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:55:22,439 INFO:  * Restarting with stat
2025-06-10 23:55:22,955 WARNING:  * Debugger is active!
2025-06-10 23:55:22,970 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:55:24,276 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:24] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:55:24,724 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:24,725 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:24,899 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,093 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:55:27,134 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,134 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,947 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:55:27,987 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,988 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:29,233 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:29] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:55:29,278 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:29,279 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:30,825 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:30] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:55:30,863 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:30,865 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:31,405 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:31] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:55:31,441 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:31,442 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:32,027 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:32] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:55:32,081 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:32,082 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:33,181 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:33] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:55:33,221 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:33,223 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:35,893 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:35] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:55:35,928 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:35,930 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:37,015 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:37] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:55:37,055 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:37,056 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:31,071 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:59:31,072 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:59:31,085 INFO:  * Restarting with stat
2025-06-10 23:59:31,657 WARNING:  * Debugger is active!
2025-06-10 23:59:31,676 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:59:32,729 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:32] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:59:32,880 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:32,881 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:32,970 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:59:54,685 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:54] "[32mPOST /admin/add_app HTTP/1.1[0m" 302 -
2025-06-10 23:59:54,715 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:54] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:59:54,755 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:54,756 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:56,937 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:56] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:59:56,971 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:56,971 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:57,035 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:57] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 00:00:13,837 INFO: 127.0.0.1 - - [11/Jun/2025 00:00:13] "GET / HTTP/1.1" 200 -
2025-06-11 00:00:14,157 INFO: 127.0.0.1 - - [11/Jun/2025 00:00:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 00:00:14,157 INFO: 127.0.0.1 - - [11/Jun/2025 00:00:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 00:02:07,049 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 00:02:07,049 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:02:07,058 INFO:  * Restarting with stat
2025-06-11 00:02:07,415 WARNING:  * Debugger is active!
2025-06-11 00:02:07,428 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:02:08,376 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-11 00:02:08,572 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,581 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,654 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,665 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:02:40,776 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 00:02:40,853 INFO:  * Restarting with stat
2025-06-11 00:04:10,743 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 00:04:10,743 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:04:10,749 INFO:  * Restarting with stat
2025-06-11 00:04:11,109 WARNING:  * Debugger is active!
2025-06-11 00:04:11,122 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:04:12,661 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:12] "GET / HTTP/1.1" 200 -
2025-06-11 00:04:13,017 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 00:04:13,019 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 00:04:13,729 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:13] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 00:04:15,106 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:04:15,134 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-11 00:04:15,147 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-11 00:04:15,211 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-11 00:04:15,226 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:31,563 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 00:08:31,564 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:08:31,574 INFO:  * Restarting with stat
2025-06-11 00:08:31,937 WARNING:  * Debugger is active!
2025-06-11 00:08:31,946 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:08:35,989 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:35] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:08:36,138 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,139 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,152 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,209 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,898 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:08:44,927 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,927 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,955 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,969 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,462 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:10:07,483 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,484 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,509 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,541 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:30,774 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 00:10:30,774 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:10:30,796 INFO:  * Restarting with stat
2025-06-11 00:10:31,246 WARNING:  * Debugger is active!
2025-06-11 00:10:31,264 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:10:33,466 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:10:33,653 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,668 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,701 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,717 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 01:06:09,594 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:06:09,595 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:06:16,122 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:16] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:16,279 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:16,280 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:19,085 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:19] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:06:19,098 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:19] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:19,150 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:19,150 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,426 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:22] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:22,464 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,464 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,517 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:06:24,728 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:24] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:06:24,738 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:24] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:24,799 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:24,800 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:08:25,938 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:08:25,939 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:08:53,267 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:08:53,325 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:08:53,596 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 01:08:53,724 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-11 01:08:53,729 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-11 01:08:54,155 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:54] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:08:54,483 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:54] "GET / HTTP/1.1" 200 -
2025-06-11 01:08:54,495 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:08:54,714 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:08:55,106 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:55] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-11 01:14:53,449 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:14:53,450 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:14:54,477 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:14:54,506 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "GET / HTTP/1.1" 200 -
2025-06-11 01:14:54,664 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:14:54,665 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:14:54,834 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
