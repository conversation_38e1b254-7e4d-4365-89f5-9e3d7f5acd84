2025-06-10 23:24:13,425 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:24:13,426 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:24:13,433 INFO:  * Restarting with stat
2025-06-10 23:24:13,972 WARNING:  * Debugger is active!
2025-06-10 23:24:13,988 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:24:18,435 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:18] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 23:24:18,482 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:18] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:24:18,919 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:18] "GET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:24:18,920 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:18] "GET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:24:19,070 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:19] "GET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:24:19,277 INFO: 127.0.0.1 - - [10/Jun/2025 23:24:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,211 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:19,453 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,547 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,562 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:19,885 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:19] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,138 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:33,471 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,471 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,736 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:33,813 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:33] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:35,875 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:35] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:36,204 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,204 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,451 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:36,529 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:36] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:40,238 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:26:40,239 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:26:40,246 INFO:  * Restarting with stat
2025-06-10 23:26:40,592 WARNING:  * Debugger is active!
2025-06-10 23:26:40,602 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:26:41,362 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:41] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:41,652 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:41,688 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:41,704 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:41] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:42,034 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:42] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,108 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:26:59,433 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,433 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,699 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:26:59,761 INFO: 127.0.0.1 - - [10/Jun/2025 23:26:59] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,393 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:00] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:27:00,722 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,722 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:00,988 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:00] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,051 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,317 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[35m[1mGET /admin HTTP/1.1[0m" 500 -
2025-06-10 23:27:01,593 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,640 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,657 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:01,980 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:01] "[36mGET /admin?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,203 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:24] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:27:24,534 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,535 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:27:24,794 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:27:28,359 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:28] "[35m[1mGET /admin/apps HTTP/1.1[0m" 500 -
2025-06-10 23:27:28,685 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:27:28,690 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:27:28,947 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:28] "GET /admin/apps?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:27:29,008 INFO: 127.0.0.1 - - [10/Jun/2025 23:27:29] "[36mGET /admin/apps?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:05,377 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-10 23:34:05,445 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET /gate HTTP/1.1" 200 -
2025-06-10 23:34:05,515 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-10 23:34:05,519 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-10 23:34:05,520 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-10 23:34:05,958 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "POST /registerf HTTP/1.1" 200 -
2025-06-10 23:34:05,996 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:05] "GET / HTTP/1.1" 200 -
2025-06-10 23:34:06,031 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:06,032 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:08,233 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:08] "GET /login HTTP/1.1" 200 -
2025-06-10 23:34:08,258 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:08,258 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:13,273 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:13] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-10 23:34:13,292 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:13] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:34:13,406 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:13,406 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:16,502 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:16] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:34:16,536 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:16,536 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:19,671 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:19,696 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:34:19,696 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:34:19,708 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "GET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:34:19,743 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:19] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,363 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:36,381 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,381 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,400 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:36,415 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:36] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,074 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:37,089 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,090 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,099 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:37,106 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:37] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:54,134 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:34:54,135 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:34:54,144 INFO:  * Restarting with stat
2025-06-10 23:34:54,565 WARNING:  * Debugger is active!
2025-06-10 23:34:54,580 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:34:56,498 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:56,615 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,616 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,634 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:56,647 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:56] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,203 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[35m[1mGET /admin/add_user HTTP/1.1[0m" 500 -
2025-06-10 23:34:57,221 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,221 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,244 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:34:57,255 INFO: 127.0.0.1 - - [10/Jun/2025 23:34:57] "[36mGET /admin/add_user?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:35:18,773 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:35:18,774 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:35:18,783 INFO:  * Restarting with stat
2025-06-10 23:35:19,143 WARNING:  * Debugger is active!
2025-06-10 23:35:19,152 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:35:19,207 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:19] "GET /admin/add_user HTTP/1.1" 200 -
2025-06-10 23:35:19,375 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:19,375 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:19,511 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:19] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-10 23:35:30,117 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:30] "[32mPOST /admin/add_user HTTP/1.1[0m" 302 -
2025-06-10 23:35:30,142 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:30] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:30,174 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:30,174 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,406 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:32] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:32,434 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,435 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:32,485 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,102 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:33,127 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,127 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,170 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,622 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:33,658 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,659 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,703 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,960 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "GET /admin/add_user HTTP/1.1" 200 -
2025-06-10 23:35:33,989 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:35:33,990 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:36,111 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:36] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:35:36,145 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:36] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:35:36,147 INFO: 127.0.0.1 - - [10/Jun/2025 23:35:36] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:43,147 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:45,713 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:45] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:36:45,762 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:36:45,774 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:36:46,179 INFO: 127.0.0.1 - - [10/Jun/2025 23:36:46] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:37:32,917 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:32] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:37:32,971 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:37:32,974 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,222 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,641 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:37:33,665 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,666 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:37:33,709 INFO: 127.0.0.1 - - [10/Jun/2025 23:37:33] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:41:26,089 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "[35m[1mGET /admin/users HTTP/1.1[0m" 500 -
2025-06-10 23:41:26,138 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:41:26,138 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:41:26,195 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "GET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:41:26,234 INFO: 127.0.0.1 - - [10/Jun/2025 23:41:26] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,829 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[35m[1mGET /admin/users HTTP/1.1[0m" 500 -
2025-06-10 23:42:03,855 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,855 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,879 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:42:03,896 INFO: 127.0.0.1 - - [10/Jun/2025 23:42:03] "[36mGET /admin/users?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:43:01,963 INFO: 127.0.0.1 - - [10/Jun/2025 23:43:01] "GET /admin/users HTTP/1.1" 200 -
2025-06-10 23:43:01,991 INFO: 127.0.0.1 - - [10/Jun/2025 23:43:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:43:01,991 INFO: 127.0.0.1 - - [10/Jun/2025 23:43:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:43:02,067 INFO: 127.0.0.1 - - [10/Jun/2025 23:43:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:44:27,550 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:27] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:44:27,586 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:44:27,586 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:44:29,841 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:44:29,872 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-10 23:44:29,879 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-10 23:44:29,891 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "GET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-10 23:44:29,900 INFO: 127.0.0.1 - - [10/Jun/2025 23:44:29] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,402 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:45:21,424 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,424 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,512 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:21,526 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:21] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,377 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:45:22,400 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,400 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,419 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:45:22,434 INFO: 127.0.0.1 - - [10/Jun/2025 23:45:22] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:47:40,025 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:47:40,027 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:47:40,037 INFO:  * Restarting with stat
2025-06-10 23:47:40,495 WARNING:  * Debugger is active!
2025-06-10 23:47:40,508 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:47:43,993 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:43] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:47:44,131 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,133 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,150 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:47:44,165 INFO: 127.0.0.1 - - [10/Jun/2025 23:47:44] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:04,381 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:48:04,382 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:48:04,387 INFO:  * Restarting with stat
2025-06-10 23:48:04,762 WARNING:  * Debugger is active!
2025-06-10 23:48:04,779 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:48:08,280 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[35m[1mGET /admin/logs HTTP/1.1[0m" 500 -
2025-06-10 23:48:08,405 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,413 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,429 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:08,451 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:08] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:48:52,251 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:48:52,252 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:48:52,258 INFO:  * Restarting with stat
2025-06-10 23:48:52,635 WARNING:  * Debugger is active!
2025-06-10 23:48:52,650 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:48:59,029 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:59] "GET /admin/logs HTTP/1.1" 200 -
2025-06-10 23:48:59,159 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:48:59,160 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:48:59,628 INFO: 127.0.0.1 - - [10/Jun/2025 23:48:59] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:49:01,893 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:01] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:49:01,928 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:01,928 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:02,545 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:02] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:49:02,573 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:02,574 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,061 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:49:03,086 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,087 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,776 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:49:03,805 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:49:03,806 INFO: 127.0.0.1 - - [10/Jun/2025 23:49:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:51:42,678 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:51:42,680 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:51:42,688 INFO:  * Restarting with stat
2025-06-10 23:51:43,077 WARNING:  * Debugger is active!
2025-06-10 23:51:43,093 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:51:47,528 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[35m[1mGET /admin/logs?type=activity HTTP/1.1[0m" 500 -
2025-06-10 23:51:47,694 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,694 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,708 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:51:47,730 INFO: 127.0.0.1 - - [10/Jun/2025 23:51:47] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:52:25,864 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:52:25,866 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:52:25,875 INFO:  * Restarting with stat
2025-06-10 23:52:26,312 WARNING:  * Debugger is active!
2025-06-10 23:52:26,326 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:52:27,570 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:27] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:52:27,710 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:27,714 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:27,807 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:27] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:52:38,568 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:38] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:52:38,602 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:38,603 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:40,154 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:40] "GET /admin/logs HTTP/1.1" 200 -
2025-06-10 23:52:40,188 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:40,188 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:41,153 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:41] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:52:41,184 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:41,185 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,063 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[35m[1mGET /admin/logs?type=download HTTP/1.1[0m" 500 -
2025-06-10 23:52:43,105 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,108 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,130 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:52:43,153 INFO: 127.0.0.1 - - [10/Jun/2025 23:52:43] "[36mGET /admin/logs?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-10 23:55:22,423 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:55:22,426 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:55:22,439 INFO:  * Restarting with stat
2025-06-10 23:55:22,955 WARNING:  * Debugger is active!
2025-06-10 23:55:22,970 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:55:24,276 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:24] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:55:24,724 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:24,725 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:24,899 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,093 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:55:27,134 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,134 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,947 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:55:27,987 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:27,988 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:29,233 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:29] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-10 23:55:29,278 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:29] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:29,279 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:30,825 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:30] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-10 23:55:30,863 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:30,865 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:31,405 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:31] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-10 23:55:31,441 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:31] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:31,442 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:31] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:32,027 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:32] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-10 23:55:32,081 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:32,082 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:33,181 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:33] "GET /admin HTTP/1.1" 200 -
2025-06-10 23:55:33,221 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:33,223 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:35,893 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:35] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:55:35,928 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:55:35,930 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:37,015 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:37] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:55:37,055 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:37] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:55:37,056 INFO: 127.0.0.1 - - [10/Jun/2025 23:55:37] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:31,071 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-10 23:59:31,072 INFO: [33mPress CTRL+C to quit[0m
2025-06-10 23:59:31,085 INFO:  * Restarting with stat
2025-06-10 23:59:31,657 WARNING:  * Debugger is active!
2025-06-10 23:59:31,676 INFO:  * Debugger PIN: 454-249-677
2025-06-10 23:59:32,729 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:32] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-10 23:59:32,880 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:32,881 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:32,970 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:32] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-10 23:59:54,685 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:54] "[32mPOST /admin/add_app HTTP/1.1[0m" 302 -
2025-06-10 23:59:54,715 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:54] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:59:54,755 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:54,756 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:56,937 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:56] "GET /admin/apps HTTP/1.1" 200 -
2025-06-10 23:59:56,971 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-10 23:59:56,971 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-10 23:59:57,035 INFO: 127.0.0.1 - - [10/Jun/2025 23:59:57] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 00:00:13,837 INFO: 127.0.0.1 - - [11/Jun/2025 00:00:13] "GET / HTTP/1.1" 200 -
2025-06-11 00:00:14,157 INFO: 127.0.0.1 - - [11/Jun/2025 00:00:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 00:00:14,157 INFO: 127.0.0.1 - - [11/Jun/2025 00:00:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 00:02:07,049 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 00:02:07,049 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:02:07,058 INFO:  * Restarting with stat
2025-06-11 00:02:07,415 WARNING:  * Debugger is active!
2025-06-11 00:02:07,428 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:02:08,376 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[35m[1mGET / HTTP/1.1[0m" 500 -
2025-06-11 00:02:08,572 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,581 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,654 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:02:08,665 INFO: 127.0.0.1 - - [11/Jun/2025 00:02:08] "[36mGET /?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:02:40,776 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 00:02:40,853 INFO:  * Restarting with stat
2025-06-11 00:04:10,743 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 00:04:10,743 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:04:10,749 INFO:  * Restarting with stat
2025-06-11 00:04:11,109 WARNING:  * Debugger is active!
2025-06-11 00:04:11,122 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:04:12,661 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:12] "GET / HTTP/1.1" 200 -
2025-06-11 00:04:13,017 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 00:04:13,019 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 00:04:13,729 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:13] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 00:04:15,106 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:04:15,134 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-11 00:04:15,147 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-11 00:04:15,211 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "GET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
2025-06-11 00:04:15,226 INFO: 127.0.0.1 - - [11/Jun/2025 00:04:15] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:31,563 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 00:08:31,564 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:08:31,574 INFO:  * Restarting with stat
2025-06-11 00:08:31,937 WARNING:  * Debugger is active!
2025-06-11 00:08:31,946 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:08:35,989 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:35] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:08:36,138 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,139 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,152 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:36,209 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:36] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,898 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:08:44,927 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,927 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,955 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:08:44,969 INFO: 127.0.0.1 - - [11/Jun/2025 00:08:44] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,462 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:10:07,483 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,484 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,509 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:07,541 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:07] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:30,774 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***********:5000
2025-06-11 00:10:30,774 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 00:10:30,796 INFO:  * Restarting with stat
2025-06-11 00:10:31,246 WARNING:  * Debugger is active!
2025-06-11 00:10:31,264 INFO:  * Debugger PIN: 454-249-677
2025-06-11 00:10:33,466 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[35m[1mGET /app/1 HTTP/1.1[0m" 500 -
2025-06-11 00:10:33,653 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,668 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,701 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 00:10:33,717 INFO: 127.0.0.1 - - [11/Jun/2025 00:10:33] "[36mGET /app/1?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1[0m" 304 -
2025-06-11 01:06:09,594 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:06:09,595 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:06:16,122 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:16] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:16,279 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:16,280 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:19,085 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:19] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:06:19,098 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:19] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:19,150 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:19,150 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,426 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:22] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:22,464 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,464 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:06:22,517 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:22] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:06:24,728 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:24] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:06:24,738 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:24] "GET / HTTP/1.1" 200 -
2025-06-11 01:06:24,799 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:06:24,800 INFO: 127.0.0.1 - - [11/Jun/2025 01:06:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:08:25,938 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:08:25,939 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:08:53,267 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:08:53,325 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:08:53,596 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-11 01:08:53,724 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-11 01:08:53,729 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:53] "GET /static/js/client.js HTTP/1.1" 200 -
2025-06-11 01:08:54,155 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:54] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:08:54,483 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:54] "GET / HTTP/1.1" 200 -
2025-06-11 01:08:54,495 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:08:54,714 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:08:55,106 INFO: 127.0.0.1 - - [11/Jun/2025 01:08:55] "GET /static/favicon.ico HTTP/1.1" 200 -
2025-06-11 01:14:53,449 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:14:53,450 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:14:54,477 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:14:54,506 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "GET / HTTP/1.1" 200 -
2025-06-11 01:14:54,664 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:14:54,665 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:14:54,834 INFO: 127.0.0.1 - - [11/Jun/2025 01:14:54] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:20:39,397 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:20:39,398 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:21:10,338 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:10] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:21:10,611 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:10] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:21:10,739 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:21:10,990 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:21:10,990 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:10] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 01:21:11,322 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:11] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:21:11,581 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:11] "GET / HTTP/1.1" 200 -
2025-06-11 01:21:11,658 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:21:11,940 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:21:25,172 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:25] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:21:25,410 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:25] "GET / HTTP/1.1" 200 -
2025-06-11 01:21:25,505 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:21:25,808 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:21:29,676 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:29] "[32mGET /app/1 HTTP/1.1[0m" 302 -
2025-06-11 01:21:29,942 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:29] "GET / HTTP/1.1" 200 -
2025-06-11 01:21:29,998 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:21:30,276 INFO: 127.0.0.1 - - [11/Jun/2025 01:21:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:26:30,780 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:26:30,781 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:26:59,548 INFO: 127.0.0.1 - - [11/Jun/2025 01:26:59] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:26:59,821 INFO: 127.0.0.1 - - [11/Jun/2025 01:26:59] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:27:00,084 INFO: 127.0.0.1 - - [11/Jun/2025 01:27:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:27:00,150 INFO: 127.0.0.1 - - [11/Jun/2025 01:27:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:27:00,151 INFO: 127.0.0.1 - - [11/Jun/2025 01:27:00] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 01:27:00,249 INFO: 127.0.0.1 - - [11/Jun/2025 01:27:00] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:27:00,600 INFO: 127.0.0.1 - - [11/Jun/2025 01:27:00] "GET / HTTP/1.1" 200 -
2025-06-11 01:27:00,844 INFO: 127.0.0.1 - - [11/Jun/2025 01:27:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:27:00,938 INFO: 127.0.0.1 - - [11/Jun/2025 01:27:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:28:23,854 INFO: 127.0.0.1 - - [11/Jun/2025 01:28:23] "[32mGET /app/2 HTTP/1.1[0m" 302 -
2025-06-11 01:28:24,079 INFO: 127.0.0.1 - - [11/Jun/2025 01:28:24] "GET / HTTP/1.1" 200 -
2025-06-11 01:28:24,172 INFO: 127.0.0.1 - - [11/Jun/2025 01:28:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:28:24,430 INFO: 127.0.0.1 - - [11/Jun/2025 01:28:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:31:22,496 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:31:22,497 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:31:47,912 INFO: 127.0.0.1 - - [11/Jun/2025 01:31:47] "GET / HTTP/1.1" 200 -
2025-06-11 01:31:48,202 INFO: 127.0.0.1 - - [11/Jun/2025 01:31:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:31:48,252 INFO: 127.0.0.1 - - [11/Jun/2025 01:31:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:32:16,718 INFO: 127.0.0.1 - - [11/Jun/2025 01:32:16] "[32mGET /app/6 HTTP/1.1[0m" 302 -
2025-06-11 01:32:16,816 INFO: 127.0.0.1 - - [11/Jun/2025 01:32:16] "GET / HTTP/1.1" 200 -
2025-06-11 01:32:17,038 INFO: 127.0.0.1 - - [11/Jun/2025 01:32:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:32:17,210 INFO: 127.0.0.1 - - [11/Jun/2025 01:32:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:35:54,414 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:35:54,415 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:36:16,353 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:16] "GET / HTTP/1.1" 200 -
2025-06-11 01:36:16,631 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:36:16,687 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:36:25,014 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:25] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:36:25,219 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:36:25,357 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:36:34,224 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:34] "[32mPOST /app/6/rate HTTP/1.1[0m" 302 -
2025-06-11 01:36:34,488 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:34] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:36:34,547 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:36:34,826 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:36:39,617 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:39] "[32mPOST /app/6/rate HTTP/1.1[0m" 302 -
2025-06-11 01:36:39,939 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:39] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:36:40,197 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:36:40,275 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:36:56,506 INFO: 127.0.0.1 - - [11/Jun/2025 01:36:56] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:37:02,003 INFO: 127.0.0.1 - - [11/Jun/2025 01:37:02] "GET / HTTP/1.1" 200 -
2025-06-11 01:37:02,342 INFO: 127.0.0.1 - - [11/Jun/2025 01:37:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:37:02,343 INFO: 127.0.0.1 - - [11/Jun/2025 01:37:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:47:17,068 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 01:47:17,084 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 01:52:39,444 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:39] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 01:52:39,721 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:39] "GET /gate HTTP/1.1" 200 -
2025-06-11 01:52:39,849 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:39] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,097 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:40] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,105 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,485 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:40] "POST /registerf HTTP/1.1" 200 -
2025-06-11 01:52:40,593 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:40] "GET / HTTP/1.1" 200 -
2025-06-11 01:52:40,742 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,821 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:40] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,930 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:52:40,972 INFO: 127.0.0.1 - - [11/Jun/2025 01:52:40] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:53:06,261 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:06] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:53:06,468 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:06,608 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:53:10,923 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:10] "GET /suggestions HTTP/1.1" 200 -
2025-06-11 01:53:11,260 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:11,260 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:53:23,328 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:23,917 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:23] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:53:24,250 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:24,251 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:53:24,513 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:24] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 01:53:30,388 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:30] "GET / HTTP/1.1" 200 -
2025-06-11 01:53:30,628 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:30,738 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:53:44,848 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:44] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 01:53:45,100 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:53:45,193 INFO: 127.0.0.1 - - [11/Jun/2025 01:53:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:55:13,417 INFO: 127.0.0.1 - - [11/Jun/2025 01:55:13] "GET /login HTTP/1.1" 200 -
2025-06-11 01:55:13,682 INFO: 127.0.0.1 - - [11/Jun/2025 01:55:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:55:13,760 INFO: 127.0.0.1 - - [11/Jun/2025 01:55:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 01:55:18,808 INFO: 127.0.0.1 - - [11/Jun/2025 01:55:18] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 01:55:18,856 INFO: 127.0.0.1 - - [11/Jun/2025 01:55:18] "[32mGET /admin HTTP/1.1[0m" 302 -
2025-06-11 01:55:19,181 INFO: 127.0.0.1 - - [11/Jun/2025 01:55:19] "GET / HTTP/1.1" 200 -
2025-06-11 01:55:19,440 INFO: 127.0.0.1 - - [11/Jun/2025 01:55:19] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 01:55:19,520 INFO: 127.0.0.1 - - [11/Jun/2025 01:55:19] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:02:58,527 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:02:58,529 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:04:40,561 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:40] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 02:04:40,831 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:40] "GET /gate HTTP/1.1" 200 -
2025-06-11 02:04:40,932 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,166 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,166 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:41] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,198 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:41] "POST /registerf HTTP/1.1" 200 -
2025-06-11 02:04:41,532 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:41] "GET / HTTP/1.1" 200 -
2025-06-11 02:04:41,533 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:41] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,772 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,863 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:41,904 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:41] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:04:44,304 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:44] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 02:04:44,504 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:44,642 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:50,033 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:50] "GET /login HTTP/1.1" 200 -
2025-06-11 02:04:50,298 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:50,378 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:53,494 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:53] "POST /login HTTP/1.1" 200 -
2025-06-11 02:04:53,820 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:53,823 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:04:58,544 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:58] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:04:58,676 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:58] "[32mGET /admin HTTP/1.1[0m" 302 -
2025-06-11 02:04:58,868 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:58] "GET / HTTP/1.1" 200 -
2025-06-11 02:04:58,991 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:04:59,241 INFO: 127.0.0.1 - - [11/Jun/2025 02:04:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:39,698 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:13:39,700 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:13:39,832 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:39] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-11 02:13:40,089 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:40] "GET /gate HTTP/1.1" 200 -
2025-06-11 02:13:40,216 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:40,441 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:40,441 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:40] "[36mGET /static/js/client.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:40,468 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:40] "POST /registerf HTTP/1.1" 200 -
2025-06-11 02:13:40,797 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:40] "GET / HTTP/1.1" 200 -
2025-06-11 02:13:40,804 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:40] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:13:41,152 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:41,152 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:41,414 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:41] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:13:43,659 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:43] "GET /login HTTP/1.1" 200 -
2025-06-11 02:13:44,002 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:44] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:44,002 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:47,966 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:47] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:13:48,071 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:48] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:13:48,286 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:48,410 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:52,281 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:52] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:13:52,615 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:52,615 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:55,023 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:55] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:13:55,358 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:55,358 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:56,740 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:56] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:13:57,087 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:57] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:57,088 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:57] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:58,647 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:58] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:13:58,999 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:59,000 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:13:59,521 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:59] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:13:59,866 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:13:59,866 INFO: 127.0.0.1 - - [11/Jun/2025 02:13:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:00,250 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:00] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 02:14:00,598 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:00,599 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:06,641 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:06] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-11 02:14:06,990 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:06,991 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:09,123 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:09] "GET / HTTP/1.1" 200 -
2025-06-11 02:14:09,511 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:09,511 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:10,871 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:10] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:14:11,206 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:11,207 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:14,597 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:14] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:14:14,929 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:14,929 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:20,158 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:20] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 02:14:20,418 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:20,497 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:14:21,400 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:21] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:14:21,745 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:14:21,746 INFO: 127.0.0.1 - - [11/Jun/2025 02:14:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:16:04,218 INFO: 127.0.0.1 - - [11/Jun/2025 02:16:04] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:16:04,480 INFO: 127.0.0.1 - - [11/Jun/2025 02:16:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:16:04,558 INFO: 127.0.0.1 - - [11/Jun/2025 02:16:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:16:04,572 INFO: 127.0.0.1 - - [11/Jun/2025 02:16:04] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:19:02,370 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:02] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:19:02,634 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:19:02,717 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:19:48,132 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:48] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:19:48,396 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:19:48,476 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:48] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:19:54,273 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:54] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:19:54,673 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:19:54,674 INFO: 127.0.0.1 - - [11/Jun/2025 02:19:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:00,154 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:00] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:20:00,402 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:00,479 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:00,495 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:00] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:20:01,677 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:01] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:20:01,947 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:02,017 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:02,036 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:02] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:20:04,481 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:04] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:20:04,739 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:04] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:04,816 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:04] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:06,097 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:06] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 02:20:06,428 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:06] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:06,428 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:06] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:09,215 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:09] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:20:09,479 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:09,541 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:12,638 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:12] "GET / HTTP/1.1" 200 -
2025-06-11 02:20:12,979 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:12,980 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:13,562 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:13] "GET / HTTP/1.1" 200 -
2025-06-11 02:20:13,897 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:13] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:13,898 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:13] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:16,417 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:16] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:20:16,745 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:16] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:16,746 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:16] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:18,613 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:18] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:20:18,878 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:18,943 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:21,780 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:21] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:20:22,146 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:22,147 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:27,849 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:27] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:20:28,112 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:28,203 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:32,467 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:32] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 02:20:32,798 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:32,799 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:34,043 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:34] "GET /admin/logs?type=admin HTTP/1.1" 200 -
2025-06-11 02:20:34,378 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:34,379 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:34,874 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:34] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:20:35,202 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:35,203 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:41,464 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:41] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-11 02:20:41,716 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:41] "GET / HTTP/1.1" 200 -
2025-06-11 02:20:41,773 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:42,056 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:43,195 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:43] "GET /login HTTP/1.1" 200 -
2025-06-11 02:20:43,539 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:43,539 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:48,635 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:48] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:20:48,723 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:48] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:20:48,965 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:49,059 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:51,368 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:51] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:20:51,760 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:51,769 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:20:52,719 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:52] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:20:53,093 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:20:53,093 INFO: 127.0.0.1 - - [11/Jun/2025 02:20:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:03,569 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:24:03,570 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:24:17,754 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:17] "[32mGET /admin/logs?type=login HTTP/1.1[0m" 302 -
2025-06-11 02:24:18,029 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:18] "GET /login HTTP/1.1" 200 -
2025-06-11 02:24:18,135 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:18,361 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:18,406 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:18] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:24:23,824 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:23] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:24:23,934 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:23] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:24:24,143 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:24,283 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:24,327 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:24] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:24:24,609 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:24] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:24,913 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:24] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:29,646 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:29] "GET /admin/edit_app/4 HTTP/1.1" 200 -
2025-06-11 02:24:29,883 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:29] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:30,082 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:41,052 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:41] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:24:41,316 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:41,393 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:45,449 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:45] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:24:45,781 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:45,781 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:45] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:46,790 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:46] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:24:47,117 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:47,118 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:48,666 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:48] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:24:48,912 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:48] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:49,007 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:24:51,010 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:51] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:24:51,347 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:24:51,348 INFO: 127.0.0.1 - - [11/Jun/2025 02:24:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:47,135 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:47] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:25:47,395 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:47] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:25:47,522 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:47] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:50,414 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:50] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:25:50,679 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:25:50,755 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:51,382 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:51] "GET / HTTP/1.1" 200 -
2025-06-11 02:25:51,721 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:25:51,721 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:55,201 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:55] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:25:55,450 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:25:55,544 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:25:59,710 INFO: 127.0.0.1 - - [11/Jun/2025 02:25:59] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:26:00,050 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:00] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-11 02:26:08,113 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:08] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:26:08,375 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:26:08,439 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:26:09,841 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:09] "GET /admin/apps HTTP/1.1" 200 -
2025-06-11 02:26:10,186 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:26:10,186 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:10] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:26:11,016 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:11] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:26:11,351 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:11] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:26:11,352 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:26:14,593 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:14] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:26:19,967 INFO: 127.0.0.1 - - [11/Jun/2025 02:26:19] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:27:11,248 INFO: 127.0.0.1 - - [11/Jun/2025 02:27:11] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:27:34,176 INFO: 127.0.0.1 - - [11/Jun/2025 02:27:34] "[33mPOST /admin/users/edit/5 HTTP/1.1[0m" 404 -
2025-06-11 02:27:50,331 INFO: 127.0.0.1 - - [11/Jun/2025 02:27:50] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:27:50,591 INFO: 127.0.0.1 - - [11/Jun/2025 02:27:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:27:50,666 INFO: 127.0.0.1 - - [11/Jun/2025 02:27:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:27:59,463 INFO: 127.0.0.1 - - [11/Jun/2025 02:27:59] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:27:59,720 INFO: 127.0.0.1 - - [11/Jun/2025 02:27:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:27:59,799 INFO: 127.0.0.1 - - [11/Jun/2025 02:27:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:01,350 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:01] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:28:01,702 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:01,703 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:13,798 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:28:13,799 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:28:14,181 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:14] "[32mGET /admin/logs?type=activity HTTP/1.1[0m" 302 -
2025-06-11 02:28:14,285 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:14] "GET /login HTTP/1.1" 200 -
2025-06-11 02:28:14,571 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:14,647 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:14,748 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:14] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:28:22,432 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:22] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:28:22,547 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:22] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:28:22,742 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:22,897 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:25,437 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:25] "GET /admin/logs HTTP/1.1" 200 -
2025-06-11 02:28:25,779 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:25,780 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:26,729 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:26] "GET /admin/logs?type=download HTTP/1.1" 200 -
2025-06-11 02:28:27,088 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:27] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:27,088 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:27] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:34,695 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:34] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:28:34,957 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:35,034 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:41,874 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:41] "GET /admin/logs?type=activity HTTP/1.1" 200 -
2025-06-11 02:28:42,138 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:42] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:42,215 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:42] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:48,877 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:48] "GET /admin/logs?type=login HTTP/1.1" 200 -
2025-06-11 02:28:50,187 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:50] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:28:50,525 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:50,526 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:28:53,776 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:53] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 02:28:54,033 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:54] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:28:54,144 INFO: 127.0.0.1 - - [11/Jun/2025 02:28:54] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:00,235 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:00] "GET / HTTP/1.1" 200 -
2025-06-11 02:29:00,576 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:00,576 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:03,531 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:03] "GET /app/6 HTTP/1.1" 200 -
2025-06-11 02:29:03,600 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:03,894 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:12,538 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:12] "POST /app/6/report HTTP/1.1" 200 -
2025-06-11 02:29:39,920 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:39] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:29:40,170 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:40] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:40,264 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:40] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:41,651 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:41] "GET /admin/edit_user/5 HTTP/1.1" 200 -
2025-06-11 02:29:41,990 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:41,990 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:45,824 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:45] "[32mPOST /admin/edit_user/5 HTTP/1.1[0m" 302 -
2025-06-11 02:29:45,938 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:45] "GET /admin/users HTTP/1.1" 200 -
2025-06-11 02:29:46,136 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:46] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:46,307 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:46] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:48,930 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:48] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-11 02:29:49,255 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:49] "GET / HTTP/1.1" 200 -
2025-06-11 02:29:49,510 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:49,605 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:50,100 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:50] "GET /login HTTP/1.1" 200 -
2025-06-11 02:29:50,441 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:50,441 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:29:52,376 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:52] "POST /login HTTP/1.1" 200 -
2025-06-11 02:29:52,717 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:29:52,717 INFO: 127.0.0.1 - - [11/Jun/2025 02:29:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:31:01,930 INFO: 127.0.0.1 - - [11/Jun/2025 02:31:01] "POST /login HTTP/1.1" 200 -
2025-06-11 02:31:02,027 INFO: 127.0.0.1 - - [11/Jun/2025 02:31:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:31:02,328 INFO: 127.0.0.1 - - [11/Jun/2025 02:31:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:31:07,243 INFO: 127.0.0.1 - - [11/Jun/2025 02:31:07] "POST /login HTTP/1.1" 200 -
2025-06-11 02:31:07,354 INFO: 127.0.0.1 - - [11/Jun/2025 02:31:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:31:07,569 INFO: 127.0.0.1 - - [11/Jun/2025 02:31:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:32:31,922 INFO: 127.0.0.1 - - [11/Jun/2025 02:32:31] "POST /login HTTP/1.1" 200 -
2025-06-11 02:32:32,019 INFO: 127.0.0.1 - - [11/Jun/2025 02:32:32] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:32:32,264 INFO: 127.0.0.1 - - [11/Jun/2025 02:32:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:32:46,991 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:32:46,992 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:32:49,413 INFO: 127.0.0.1 - - [11/Jun/2025 02:32:49] "POST /login HTTP/1.1" 200 -
2025-06-11 02:32:49,660 INFO: 127.0.0.1 - - [11/Jun/2025 02:32:49] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:32:49,750 INFO: 127.0.0.1 - - [11/Jun/2025 02:32:49] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:33:04,400 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:33:04,401 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:33:07,529 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:33:07,530 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:33:07,537 INFO:  * Restarting with stat
2025-06-11 02:33:07,916 WARNING:  * Debugger is active!
2025-06-11 02:33:07,928 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:33:10,762 INFO: 127.0.0.1 - - [11/Jun/2025 02:33:10] "POST /login HTTP/1.1" 200 -
2025-06-11 02:33:10,968 INFO: 127.0.0.1 - - [11/Jun/2025 02:33:10] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:33:11,093 INFO: 127.0.0.1 - - [11/Jun/2025 02:33:11] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:33:22,560 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:33:22,716 INFO:  * Restarting with stat
2025-06-11 02:33:23,342 WARNING:  * Debugger is active!
2025-06-11 02:33:23,352 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:33:25,528 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:33:25,577 INFO:  * Restarting with stat
2025-06-11 02:33:25,985 WARNING:  * Debugger is active!
2025-06-11 02:33:25,996 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:34:32,432 INFO: 127.0.0.1 - - [11/Jun/2025 02:34:32] "POST /login HTTP/1.1" 200 -
2025-06-11 02:34:37,395 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:34:37,469 INFO:  * Restarting with stat
2025-06-11 02:34:38,094 WARNING:  * Debugger is active!
2025-06-11 02:34:38,136 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:35:06,535 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:06] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:35:06,648 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:06] "[32mGET /publisher HTTP/1.1[0m" 302 -
2025-06-11 02:35:06,999 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:06] "GET / HTTP/1.1" 200 -
2025-06-11 02:35:07,616 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:07] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:35:07,947 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:07] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:35:08,477 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:08] "[36mGET /static/favicon.ico HTTP/1.1[0m" 304 -
2025-06-11 02:35:11,748 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:11] "[32mGET /publisher HTTP/1.1[0m" 302 -
2025-06-11 02:35:12,135 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:12] "GET / HTTP/1.1" 200 -
2025-06-11 02:35:12,450 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:35:12,762 INFO: 127.0.0.1 - - [11/Jun/2025 02:35:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:36:20,535 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:36:20,630 INFO:  * Restarting with stat
2025-06-11 02:36:21,213 WARNING:  * Debugger is active!
2025-06-11 02:36:21,253 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:36:38,015 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:36:38,094 INFO:  * Restarting with stat
2025-06-11 02:36:38,826 WARNING:  * Debugger is active!
2025-06-11 02:36:38,900 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:37:21,441 INFO:  * Detected change in 'C:\\Users\\<USER>\\Desktop\\sell project\\Tools store\\app\\views\\routes.py', reloading
2025-06-11 02:37:21,542 INFO:  * Restarting with stat
2025-06-11 02:37:22,228 WARNING:  * Debugger is active!
2025-06-11 02:37:22,251 INFO:  * Debugger PIN: 454-249-677
2025-06-11 02:38:46,116 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:38:46,117 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:39:07,866 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:07] "[32mGET /admin HTTP/1.1[0m" 302 -
2025-06-11 02:39:07,989 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:07] "GET /login HTTP/1.1" 200 -
2025-06-11 02:39:08,254 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:39:08,361 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:08] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:39:20,117 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:20] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:39:20,201 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:20] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:39:20,433 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:39:20,559 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:39:24,776 ERROR: Exception on /publisher/edit_app/5 [GET]
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\views\routes.py", line 954, in publisher_edit_app
    return render_template('publisher/edit_app.html', app=app, categories=Config.CATEGORIES)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\publisher\edit_app.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\base.html", line 114, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\sell project\Tools store\app\templates\publisher\edit_app.html", line 153, in block 'content'
    {% if app.has_file() %}
    ^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\jinja2\utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'dict object' has no attribute 'has_file'
2025-06-11 02:39:24,797 INFO: 127.0.0.1 - - [11/Jun/2025 02:39:24] "[35m[1mGET /publisher/edit_app/5 HTTP/1.1[0m" 500 -
2025-06-11 02:40:48,188 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:40:48,189 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:40:51,878 INFO: 127.0.0.1 - - [11/Jun/2025 02:40:51] "[32mGET /publisher/edit_app/5 HTTP/1.1[0m" 302 -
2025-06-11 02:40:52,150 INFO: 127.0.0.1 - - [11/Jun/2025 02:40:52] "GET /login HTTP/1.1" 200 -
2025-06-11 02:40:52,259 INFO: 127.0.0.1 - - [11/Jun/2025 02:40:52] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:40:52,496 INFO: 127.0.0.1 - - [11/Jun/2025 02:40:52] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:40:57,938 INFO: 127.0.0.1 - - [11/Jun/2025 02:40:57] "POST /login HTTP/1.1" 200 -
2025-06-11 02:40:58,040 INFO: 127.0.0.1 - - [11/Jun/2025 02:40:58] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:40:58,271 INFO: 127.0.0.1 - - [11/Jun/2025 02:40:58] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:41:01,363 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:01] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:41:01,490 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:01] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:41:01,676 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:01] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:41:01,825 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:01] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:41:05,023 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:05] "GET /publisher/edit_app/5 HTTP/1.1" 200 -
2025-06-11 02:41:05,275 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:41:05,401 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:41:14,665 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:14] "[32mGET /app/5 HTTP/1.1[0m" 302 -
2025-06-11 02:41:14,930 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:14] "GET / HTTP/1.1" 200 -
2025-06-11 02:41:14,977 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:41:15,284 INFO: 127.0.0.1 - - [11/Jun/2025 02:41:15] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:08,800 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:42:08,801 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:42:28,376 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:28] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 02:42:28,590 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:28] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:28,705 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:28] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:42:41,370 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:41] "[32mGET /admin HTTP/1.1[0m" 302 -
2025-06-11 02:42:41,635 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:41] "GET /login HTTP/1.1" 200 -
2025-06-11 02:42:41,681 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:41,976 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:42:52,719 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:52] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:42:52,793 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:52] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:42:53,040 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:53] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:53,150 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:53] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:42:56,452 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:56] "GET /publisher/edit_app/5 HTTP/1.1" 200 -
2025-06-11 02:42:56,707 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:56] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:42:56,784 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:56] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:42:59,740 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:59] "GET /app/5 HTTP/1.1" 200 -
2025-06-11 02:42:59,978 INFO: 127.0.0.1 - - [11/Jun/2025 02:42:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:00,086 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:11,792 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:11] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:43:12,052 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:12] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:12,159 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:12] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:13,733 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:13] "GET /publisher/edit_app/5 HTTP/1.1" 200 -
2025-06-11 02:43:14,069 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:14] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:14,070 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:14] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:21,384 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:21] "GET / HTTP/1.1" 200 -
2025-06-11 02:43:21,614 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:21,737 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:22,116 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:22] "GET / HTTP/1.1" 200 -
2025-06-11 02:43:22,452 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:22] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:22,453 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:22] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:25,148 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:25] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 02:43:25,408 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:25,500 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:26,323 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:26] "[32mGET /download/1 HTTP/1.1[0m" 302 -
2025-06-11 02:43:30,592 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:30] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 02:43:30,833 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:30] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:30,942 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:30] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:32,782 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:32] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:43:33,120 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:33,121 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:33] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:43:34,662 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:34] "GET /publisher/add_app HTTP/1.1" 200 -
2025-06-11 02:43:34,922 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:43:34,999 INFO: 127.0.0.1 - - [11/Jun/2025 02:43:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:50:05,075 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 02:50:05,103 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 02:50:25,169 INFO: 127.0.0.1 - - [11/Jun/2025 02:50:25] "[32mGET /admin/add_app HTTP/1.1[0m" 302 -
2025-06-11 02:50:25,437 INFO: 127.0.0.1 - - [11/Jun/2025 02:50:25] "GET /login HTTP/1.1" 200 -
2025-06-11 02:50:25,598 INFO: 127.0.0.1 - - [11/Jun/2025 02:50:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:50:25,809 INFO: 127.0.0.1 - - [11/Jun/2025 02:50:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:54:35,077 INFO: 127.0.0.1 - - [11/Jun/2025 02:54:35] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:54:35,427 INFO: 127.0.0.1 - - [11/Jun/2025 02:54:35] "GET /publisher HTTP/1.1" 200 -
2025-06-11 02:54:35,670 INFO: 127.0.0.1 - - [11/Jun/2025 02:54:35] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:54:35,807 INFO: 127.0.0.1 - - [11/Jun/2025 02:54:35] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:54:38,343 INFO: 127.0.0.1 - - [11/Jun/2025 02:54:38] "GET /publisher/add_app HTTP/1.1" 200 -
2025-06-11 02:54:38,693 INFO: 127.0.0.1 - - [11/Jun/2025 02:54:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:54:38,694 INFO: 127.0.0.1 - - [11/Jun/2025 02:54:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:12,320 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:12] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-06-11 02:55:12,651 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:12] "GET / HTTP/1.1" 200 -
2025-06-11 02:55:17,691 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:17] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:18,007 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:55:20,766 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:20] "GET /login HTTP/1.1" 200 -
2025-06-11 02:55:21,024 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:21] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:55:21,298 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:21] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:29,524 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:29] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 02:55:30,815 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:30] "GET /admin HTTP/1.1" 200 -
2025-06-11 02:55:32,749 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:32] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:33,076 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 02:55:38,651 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:38] "GET /admin/add_app HTTP/1.1" 200 -
2025-06-11 02:55:44,971 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:44] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 02:55:45,367 INFO: 127.0.0.1 - - [11/Jun/2025 02:55:45] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:01:41,358 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 03:01:41,360 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:02:01,755 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:01] "[32mGET /publisher/add_app HTTP/1.1[0m" 302 -
2025-06-11 03:02:02,123 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:02] "GET /login HTTP/1.1" 200 -
2025-06-11 03:02:03,196 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:03] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:02:03,330 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:03] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:02:17,925 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:17] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-11 03:02:17,983 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:17] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:02:18,412 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:18] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:02:18,413 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:18] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:02:23,391 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:23] "GET /publisher/add_app HTTP/1.1" 200 -
2025-06-11 03:02:23,817 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:23] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:02:23,818 INFO: 127.0.0.1 - - [11/Jun/2025 03:02:23] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:03:05,187 INFO: 127.0.0.1 - - [11/Jun/2025 03:03:05] "[32mPOST /publisher/add_app HTTP/1.1[0m" 302 -
2025-06-11 03:03:05,578 INFO: 127.0.0.1 - - [11/Jun/2025 03:03:05] "GET /publisher HTTP/1.1" 200 -
2025-06-11 03:03:05,838 INFO: 127.0.0.1 - - [11/Jun/2025 03:03:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:03:05,965 INFO: 127.0.0.1 - - [11/Jun/2025 03:03:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:03:08,330 INFO: 127.0.0.1 - - [11/Jun/2025 03:03:08] "[32mGET /app/9 HTTP/1.1[0m" 302 -
2025-06-11 03:03:08,697 INFO: 127.0.0.1 - - [11/Jun/2025 03:03:08] "GET / HTTP/1.1" 200 -
2025-06-11 03:03:08,932 INFO: 127.0.0.1 - - [11/Jun/2025 03:03:08] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:03:09,080 INFO: 127.0.0.1 - - [11/Jun/2025 03:03:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:05:38,593 INFO: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-06-11 03:05:38,595 INFO: [33mPress CTRL+C to quit[0m
2025-06-11 03:06:01,819 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:01] "GET /app/1 HTTP/1.1" 200 -
2025-06-11 03:06:02,040 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:02] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:02,207 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:02] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:05,348 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:05] "GET / HTTP/1.1" 200 -
2025-06-11 03:06:05,558 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:05] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:05,718 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:05] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:05,720 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:05] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:06:09,108 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:09] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 03:06:09,464 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:09] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:09,474 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:09] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:09,476 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:09] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:06:17,166 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:17] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:20,037 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:20] "GET / HTTP/1.1" 200 -
2025-06-11 03:06:20,278 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:20] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:20,408 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:20] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:20,410 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:20] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:06:25,272 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:25] "GET /app/9 HTTP/1.1" 200 -
2025-06-11 03:06:25,644 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:25] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-11 03:06:25,645 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:25] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-11 03:06:25,646 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:25] "[33mGET /uploads/icons\\20250611_030305_d73ff7ad-c496-4746-913d-cd1168f5276b.jpg HTTP/1.1[0m" 404 -
2025-06-11 03:06:33,877 INFO: 127.0.0.1 - - [11/Jun/2025 03:06:33] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
