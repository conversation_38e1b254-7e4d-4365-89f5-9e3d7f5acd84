{% extends "base.html" %}

{% block title %}Add New App - Publisher Dashboard{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4><i class="bi bi-plus-circle"></i> Add New App</h4>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">App Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="version" class="form-label">Version *</label>
                                    <input type="text" class="form-control" id="version" name="version" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="developer" class="form-label">Developer *</label>
                                    <input type="text" class="form-control" id="developer" name="developer" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category *</label>
                                    <select class="form-control" id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        {% for category in categories %}
                                        <option value="{{ category }}">{{ category }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">Short Description *</label>
                            <input type="text" class="form-control" id="short_description" name="short_description" 
                                   maxlength="200" required placeholder="Brief description (max 200 characters)">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Full Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="6" required
                                      placeholder="Detailed description of your app..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price (USD)</label>
                                    <input type="number" class="form-control" id="price" name="price"
                                           min="0" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="external_url" class="form-label">Download URL</label>
                                    <input type="url" class="form-control" id="external_url" name="external_url"
                                           placeholder="https://example.com/download">
                                </div>
                            </div>
                        </div>

                        <!-- Media Files -->
                        <h5 class="mb-3 mt-4">Media Files</h5>
                        <div class="mb-3">
                            <label for="icon" class="form-label">App Icon</label>
                            <input type="file" class="form-control" id="icon" name="icon"
                                   accept=".png,.jpg,.jpeg,.gif">
                            <div class="form-text">Recommended size: 512x512px (PNG, JPG, GIF). Will be compressed automatically.</div>
                        </div>

                        <div class="mb-3">
                            <label for="screenshots" class="form-label">Screenshots</label>
                            <input type="file" class="form-control" id="screenshots" name="screenshots"
                                   accept=".png,.jpg,.jpeg,.gif" multiple>
                            <div class="form-text">Upload multiple screenshots to showcase your app (max 10). Will be compressed automatically.</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('views.publisher_dashboard') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Add App
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
// Icon validation
document.getElementById('icon').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Check file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('Icon must be a PNG, JPG, or GIF image');
            e.target.value = '';
            return;
        }

        // Check file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('Icon file size must be less than 5MB');
            e.target.value = '';
            return;
        }
    }
});

// Screenshots validation
document.getElementById('screenshots').addEventListener('change', function(e) {
    const files = e.target.files;
    if (files.length > 10) {
        alert('Maximum 10 screenshots allowed');
        e.target.value = '';
        return;
    }

    for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Check file type
        const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert(`Screenshot ${i + 1} must be a PNG, JPG, or GIF image`);
            e.target.value = '';
            return;
        }

        // Check file size (max 10MB per screenshot)
        if (file.size > 10 * 1024 * 1024) {
            alert(`Screenshot ${i + 1} file size must be less than 10MB`);
            e.target.value = '';
            return;
        }
    }
});

// Character counter for short description
document.getElementById('short_description').addEventListener('input', function(e) {
    const maxLength = 200;
    const currentLength = e.target.value.length;
    const remaining = maxLength - currentLength;

    // Update placeholder or add counter
    if (remaining < 20) {
        e.target.style.borderColor = '#ffc107';
    } else {
        e.target.style.borderColor = '';
    }
});
</script>
{% endblock %}
