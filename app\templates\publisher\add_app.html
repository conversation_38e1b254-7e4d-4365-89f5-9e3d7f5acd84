{% extends "base.html" %}

{% block title %}Add New App - Publisher Dashboard{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4><i class="bi bi-plus-circle"></i> Add New App</h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">App Name *</label>
                                    <input type="text" class="form-control" id="name" name="name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="version" class="form-label">Version *</label>
                                    <input type="text" class="form-control" id="version" name="version" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="developer" class="form-label">Developer *</label>
                                    <input type="text" class="form-control" id="developer" name="developer" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category *</label>
                                    <select class="form-control" id="category" name="category" required>
                                        <option value="">Select Category</option>
                                        {% for category in categories %}
                                        <option value="{{ category }}">{{ category }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="short_description" class="form-label">Short Description *</label>
                            <input type="text" class="form-control" id="short_description" name="short_description" 
                                   maxlength="200" required placeholder="Brief description (max 200 characters)">
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Full Description *</label>
                            <textarea class="form-control" id="description" name="description" rows="6" required
                                      placeholder="Detailed description of your app..."></textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="price" class="form-label">Price (USD)</label>
                                    <input type="number" class="form-control" id="price" name="price" 
                                           min="0" step="0.01" value="0">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="external_url" class="form-label">Download URL</label>
                                    <input type="url" class="form-control" id="external_url" name="external_url"
                                           placeholder="https://example.com/download">
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('views.publisher_dashboard') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> Back to Dashboard
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> Add App
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
