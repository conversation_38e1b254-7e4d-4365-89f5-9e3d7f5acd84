{% extends "base.html" %}

{% block title %}Add New Post - Publisher Dashboard{% endblock %}

{% block content %}
<div class="container my-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('views.index') }}">Home</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('views.publisher_dashboard') }}">Publisher</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('views.publisher_posts') }}">Posts</a></li>
            <li class="breadcrumb-item active">Add Post</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8">
            <!-- Add Post Form -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="bi bi-plus"></i> Create New Post</h3>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="title" class="form-label">Title:</label>
                            <input type="text" class="form-control" id="title" name="title" required
                                   placeholder="Enter post title">
                        </div>
                        
                        <div class="mb-3">
                            <label for="excerpt" class="form-label">Excerpt (Optional):</label>
                            <textarea class="form-control" id="excerpt" name="excerpt" rows="2"
                                      placeholder="Brief summary of your post..."></textarea>
                            <small class="text-muted">This will be shown in post previews and search results.</small>
                        </div>
                        
                        <div class="mb-3">
                            <label for="content" class="form-label">Content:</label>
                            <textarea class="form-control" id="content" name="content" rows="15" required
                                      placeholder="Write your post content here..."></textarea>
                            <small class="text-muted">You can use Markdown formatting.</small>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category" class="form-label">Category:</label>
                                    <select class="form-select" id="category" name="category" required>
                                        {% for cat in categories %}
                                        <option value="{{ cat }}">{{ cat.replace('_', ' ').title() }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status:</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="draft">Draft</option>
                                        <option value="published">Published</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="tags" class="form-label">Tags (Optional):</label>
                            <input type="text" class="form-control" id="tags" name="tags"
                                   placeholder="tag1, tag2, tag3">
                            <small class="text-muted">Separate tags with commas.</small>
                        </div>
                        
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="{{ url_for('views.publisher_posts') }}" class="btn btn-secondary me-md-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> Create Post
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <!-- Writing Tips -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="bi bi-lightbulb"></i> Writing Tips</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li><i class="bi bi-check-circle text-success"></i> Use clear, descriptive titles</li>
                        <li><i class="bi bi-check-circle text-success"></i> Write engaging excerpts</li>
                        <li><i class="bi bi-check-circle text-success"></i> Structure content with headings</li>
                        <li><i class="bi bi-check-circle text-success"></i> Use relevant tags</li>
                        <li><i class="bi bi-check-circle text-success"></i> Proofread before publishing</li>
                    </ul>
                </div>
            </div>
            
            <!-- Markdown Guide -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-markdown"></i> Markdown Guide</h5>
                </div>
                <div class="card-body">
                    <small>
                        <strong>Headers:</strong><br>
                        <code># H1</code><br>
                        <code>## H2</code><br>
                        <code>### H3</code><br><br>
                        
                        <strong>Text Formatting:</strong><br>
                        <code>**bold**</code><br>
                        <code>*italic*</code><br>
                        <code>`code`</code><br><br>
                        
                        <strong>Lists:</strong><br>
                        <code>- Item 1</code><br>
                        <code>- Item 2</code><br><br>
                        
                        <strong>Links:</strong><br>
                        <code>[text](url)</code><br><br>
                        
                        <strong>Code Blocks:</strong><br>
                        <code>```<br>code here<br>```</code>
                    </small>
                </div>
            </div>
            
            <!-- Category Guide -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5><i class="bi bi-tags"></i> Categories</h5>
                </div>
                <div class="card-body">
                    <small>
                        <strong>General:</strong> General discussions and topics<br>
                        <strong>Tutorial:</strong> Step-by-step guides<br>
                        <strong>Review:</strong> App and tool reviews<br>
                        <strong>News:</strong> Industry news and updates<br>
                        <strong>Announcement:</strong> Important announcements<br>
                        <strong>Guide:</strong> How-to guides and tips<br>
                        <strong>Tips:</strong> Quick tips and tricks<br>
                        <strong>Security:</strong> Security-related content<br>
                        <strong>Development:</strong> Development topics
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-resize textarea
document.getElementById('content').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});

// Character counter for excerpt
document.getElementById('excerpt').addEventListener('input', function() {
    const maxLength = 200;
    const currentLength = this.value.length;
    const remaining = maxLength - currentLength;
    
    // Create or update counter
    let counter = document.getElementById('excerpt-counter');
    if (!counter) {
        counter = document.createElement('small');
        counter.id = 'excerpt-counter';
        counter.className = 'text-muted';
        this.parentNode.appendChild(counter);
    }
    
    counter.textContent = `${currentLength}/${maxLength} characters`;
    
    if (remaining < 0) {
        counter.className = 'text-danger';
    } else if (remaining < 20) {
        counter.className = 'text-warning';
    } else {
        counter.className = 'text-muted';
    }
});

// Auto-generate excerpt from content if empty
document.getElementById('content').addEventListener('blur', function() {
    const excerpt = document.getElementById('excerpt');
    if (!excerpt.value.trim() && this.value.trim()) {
        // Extract first paragraph or first 150 characters
        let autoExcerpt = this.value.trim();
        
        // Remove markdown formatting
        autoExcerpt = autoExcerpt.replace(/[#*`_\[\]()]/g, '');
        
        // Get first paragraph or first 150 characters
        const firstParagraph = autoExcerpt.split('\n')[0];
        if (firstParagraph.length > 150) {
            autoExcerpt = firstParagraph.substring(0, 147) + '...';
        } else {
            autoExcerpt = firstParagraph;
        }
        
        excerpt.value = autoExcerpt;
        excerpt.dispatchEvent(new Event('input')); // Trigger character counter
    }
});
</script>
{% endblock %}
