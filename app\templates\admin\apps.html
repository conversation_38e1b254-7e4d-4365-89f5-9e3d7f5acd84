{% extends "base.html" %}

{% block title %}Manage Apps - Admin{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-list"></i> Manage Apps</h1>
        <div>
            <a href="{{ url_for('views.admin_dashboard') }}" class="btn btn-outline-secondary me-2">
                <i class="bi bi-arrow-left"></i> Back to Dashboard
            </a>
            <a href="{{ url_for('views.admin_add_app') }}" class="btn btn-success">
                <i class="bi bi-plus-circle"></i> Add New App
            </a>
        </div>
    </div>

    {% if apps.items %}
    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Icon</th>
                            <th>Name</th>
                            <th>Developer</th>
                            <th>Uploaded By</th>
                            <th>Category</th>
                            <th>Downloads</th>
                            <th>Price</th>
                            <th>Featured</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for app in apps.items %}
                        <tr>
                            <td>
                                {% if app.icon_path %}
                                <img src="{{ url_for('uploaded_file', filename=app.icon_path) }}"
                                     class="admin-app-icon" alt="{{ app.name }}">
                                {% else %}
                                <div class="admin-app-icon-placeholder">
                                    <i class="bi bi-app"></i>
                                </div>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ app.name }}</strong><br>
                                <small class="text-muted">{{ app.short_description[:50] }}{% if app.short_description|length > 50 %}...{% endif %}</small>
                            </td>
                            <td>{{ app.developer }}</td>
                            <td>{{ app.uploaded_by }}</td>
                            <td><span class="badge bg-secondary">{{ app.category }}</span></td>
                            <td>{{ app.downloads }}</td>
                            <td>
                                {% if app.price == 0 %}
                                <span class="text-success">Free</span>
                                {% else %}
                                ${{ "%.2f"|format(app.price) }}
                                {% endif %}
                            </td>
                            <td>
                                {% if app.is_featured %}
                                <span class="badge bg-warning text-dark">
                                    <i class="bi bi-star-fill"></i> Featured
                                </span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('views.app_detail', app_id=app.id) }}"
                                       class="btn btn-sm btn-outline-info" title="View">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="{{ url_for('views.admin_edit_app', app_id=app.id) }}"
                                       class="btn btn-sm btn-outline-primary" title="Edit">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="confirmDelete({{ app.id }}, '{{ app.name }}')" title="Delete">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if apps.pages > 1 %}
    <nav aria-label="Apps pagination" class="mt-4">
        <ul class="pagination justify-content-center">
            {% if apps.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('views.admin_apps', page=apps.prev_num) }}">Previous</a>
            </li>
            {% endif %}

            {% for page_num in apps.iter_pages() %}
                {% if page_num %}
                    {% if page_num != apps.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('views.admin_apps', page=page_num) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}

            {% if apps.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('views.admin_apps', page=apps.next_num) }}">Next</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}

    {% else %}
    <div class="text-center py-5">
        <i class="bi bi-app display-1 text-muted"></i>
        <h3 class="mt-3">No apps yet</h3>
        <p class="text-muted">Start by adding your first app to the store.</p>
        <a href="{{ url_for('views.admin_add_app') }}" class="btn btn-success">
            <i class="bi bi-plus-circle"></i> Add First App
        </a>
    </div>
    {% endif %}
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the app "<span id="deleteAppName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(appId, appName) {
    document.getElementById('deleteAppName').textContent = appName;
    document.getElementById('deleteForm').action = `/admin/delete_app/${appId}`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}

{% block extra_head %}
<style>
.admin-app-icon {
    width: 48px;
    height: 48px;
    object-fit: cover;
    border-radius: 0.375rem;
}

.admin-app-icon-placeholder {
    width: 48px;
    height: 48px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: #6c757d;
    border-radius: 0.375rem;
}
</style>
{% endblock %}
